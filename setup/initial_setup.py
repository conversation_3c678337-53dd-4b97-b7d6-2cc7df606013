"""
Initial setup process for collecting and storing user data and preferences.
"""

import os
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
import click
from loguru import logger

from config.config_manager import ConfigManager
from .data_validator import DataValidator


class InitialSetup:
    """Handles the initial setup process for the job application system."""
    
    def __init__(self):
        """Initialize setup manager."""
        self.config_manager = ConfigManager()
        self.validator = DataValidator()
        self.user_data = {}
        self.config_data = {}
    
    async def run_setup(self) -> None:
        """Run the complete setup process."""
        click.echo("🎯 Welcome to the Job Application Automation System!")
        click.echo("This setup will collect your information for automated job applications.\n")
        
        try:
            # Check if setup already exists
            if self.config_manager.is_setup_complete():
                if not click.confirm("Setup already exists. Do you want to reconfigure?"):
                    return
            
            # Collect user data
            await self._collect_personal_info()
            await self._collect_professional_info()
            await self._collect_job_preferences()
            await self._collect_application_responses()
            await self._collect_notion_config()
            await self._collect_ai_config()
            
            # Validate all data
            self._validate_setup_data()
            
            # Save configuration
            self._save_setup_data()
            
            click.echo("\n✅ Setup completed successfully!")
            click.echo("Your data has been encrypted and stored securely.")
            click.echo("You can now run job applications with: python main.py apply")
            
        except KeyboardInterrupt:
            click.echo("\n⏹️  Setup cancelled by user.")
            raise
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            click.echo(f"\n❌ Setup failed: {e}")
            raise
    
    async def _collect_personal_info(self) -> None:
        """Collect personal information."""
        click.echo("📋 Personal Information")
        click.echo("-" * 50)
        
        # Name
        while True:
            name = click.prompt("Full Name")
            valid, msg = self.validator.validate_name(name)
            if valid:
                self.user_data["name"] = name.strip()
                break
            click.echo(f"❌ {msg}")
        
        # Email
        while True:
            email = click.prompt("Email Address")
            valid, msg = self.validator.validate_email(email)
            if valid:
                self.user_data["email"] = email.strip().lower()
                break
            click.echo(f"❌ {msg}")
        
        # Phone
        while True:
            phone = click.prompt("Phone Number")
            valid, msg = self.validator.validate_phone(phone)
            if valid:
                self.user_data["phone"] = phone.strip()
                break
            click.echo(f"❌ {msg}")
        
        # Address
        self.user_data["address"] = {
            "street": click.prompt("Street Address", default=""),
            "city": click.prompt("City"),
            "state": click.prompt("State/Province"),
            "zip_code": click.prompt("ZIP/Postal Code"),
            "country": click.prompt("Country", default="United States")
        }
        
        click.echo("✅ Personal information collected\n")
    
    async def _collect_professional_info(self) -> None:
        """Collect professional information."""
        click.echo("💼 Professional Information")
        click.echo("-" * 50)
        
        # Resume file
        while True:
            resume_path = click.prompt("Resume file path (PDF only)")
            # Expand user path
            resume_path = os.path.expanduser(resume_path)
            valid, msg = self.validator.validate_resume_file(resume_path)
            if valid:
                self.user_data["resume_path"] = resume_path
                break
            click.echo(f"❌ {msg}")
        
        # LinkedIn URL
        while True:
            linkedin_url = click.prompt("LinkedIn Profile URL")
            valid, msg = self.validator.validate_linkedin_url(linkedin_url)
            if valid:
                self.user_data["linkedin_url"] = linkedin_url.strip()
                break
            click.echo(f"❌ {msg}")
        
        # GitHub URL (optional)
        github_url = click.prompt("GitHub Profile URL (optional)", default="")
        if github_url:
            valid, msg = self.validator.validate_github_url(github_url)
            if valid:
                self.user_data["github_url"] = github_url.strip()
            else:
                click.echo(f"❌ {msg}")
                self.user_data["github_url"] = ""
        else:
            self.user_data["github_url"] = ""
        
        # Portfolio/Website (optional)
        portfolio_url = click.prompt("Portfolio/Website URL (optional)", default="")
        if portfolio_url:
            valid, msg = self.validator.validate_url(portfolio_url)
            if valid:
                self.user_data["portfolio_url"] = portfolio_url.strip()
            else:
                click.echo(f"❌ {msg}")
                self.user_data["portfolio_url"] = ""
        else:
            self.user_data["portfolio_url"] = ""
        
        # Education
        self.user_data["education"] = {
            "degree": click.prompt("Degree (e.g., Bachelor of Science in Computer Science)"),
            "university": click.prompt("University/College"),
            "graduation_date": click.prompt("Graduation Date (MM/YYYY)"),
            "gpa": click.prompt("GPA (optional)", default="")
        }
        
        # Skills
        skills_input = click.prompt("Technical Skills (comma-separated)")
        self.user_data["skills"] = [skill.strip() for skill in skills_input.split(",") if skill.strip()]
        
        click.echo("✅ Professional information collected\n")

    async def _collect_job_preferences(self) -> None:
        """Collect job search preferences."""
        click.echo("🎯 Job Search Preferences")
        click.echo("-" * 50)

        # Job keywords
        click.echo("Enter job titles/keywords you're interested in:")
        keywords = []
        while True:
            keyword = click.prompt(f"Job keyword #{len(keywords) + 1} (or press Enter to finish)", default="")
            if not keyword:
                break
            keywords.append(keyword.strip())

        if not keywords:
            # Use defaults
            from config.settings import DEFAULT_JOB_KEYWORDS
            keywords = DEFAULT_JOB_KEYWORDS
            click.echo(f"Using default keywords: {', '.join(keywords)}")

        # Locations
        click.echo("\nEnter preferred job locations:")
        locations = []
        while True:
            location = click.prompt(f"Location #{len(locations) + 1} (or press Enter to finish)", default="")
            if not location:
                break
            locations.append(location.strip())

        if not locations:
            # Use defaults
            from config.settings import DEFAULT_LOCATIONS
            locations = DEFAULT_LOCATIONS
            click.echo(f"Using default locations: {', '.join(locations)}")

        # Experience levels
        experience_levels = click.prompt(
            "Experience levels (comma-separated)",
            default="Internship, Entry level"
        ).split(",")
        experience_levels = [level.strip() for level in experience_levels]

        # Job types
        job_types = click.prompt(
            "Job types (comma-separated)",
            default="Full-time, Part-time, Internship"
        ).split(",")
        job_types = [jtype.strip() for jtype in job_types]

        # Remote preference
        remote_pref = click.prompt(
            "Remote work preference",
            default="Remote, Hybrid, On-site"
        )

        # Salary range (optional)
        salary_min = click.prompt("Minimum salary (optional, numbers only)", default="")
        salary_max = click.prompt("Maximum salary (optional, numbers only)", default="")

        try:
            salary_min = int(salary_min) if salary_min else None
            salary_max = int(salary_max) if salary_max else None
        except ValueError:
            salary_min = salary_max = None
            click.echo("❌ Invalid salary format, skipping salary preferences")

        self.config_data["job_preferences"] = {
            "keywords": keywords,
            "locations": locations,
            "experience_levels": experience_levels,
            "job_types": job_types,
            "remote_preference": remote_pref,
            "salary_min": salary_min,
            "salary_max": salary_max
        }

        click.echo("✅ Job preferences collected\n")

    async def _collect_application_responses(self) -> None:
        """Collect standard application question responses."""
        click.echo("📝 Standard Application Responses")
        click.echo("-" * 50)

        # Work authorization
        work_auth_options = [
            "US Citizen",
            "Permanent Resident",
            "H1B Visa",
            "F1 Visa (OPT)",
            "Other Visa",
            "Require Sponsorship"
        ]

        click.echo("Work Authorization Status:")
        for i, option in enumerate(work_auth_options, 1):
            click.echo(f"{i}. {option}")

        while True:
            try:
                choice = int(click.prompt("Select option (1-6)"))
                if 1 <= choice <= 6:
                    self.user_data["work_authorization"] = work_auth_options[choice - 1]
                    break
                else:
                    click.echo("❌ Please select a number between 1 and 6")
            except ValueError:
                click.echo("❌ Please enter a valid number")

        # Sponsorship
        self.user_data["requires_sponsorship"] = click.confirm("Do you require visa sponsorship?")

        # Availability
        self.user_data["availability"] = {
            "start_date": click.prompt("Earliest start date (MM/DD/YYYY)"),
            "notice_period": click.prompt("Notice period (e.g., '2 weeks', 'Immediate')", default="Immediate")
        }

        # Cover letter template
        click.echo("\nCover Letter Template:")
        click.echo("Enter a brief cover letter template (will be customized per application):")
        cover_letter = click.prompt("Cover letter", default="")
        self.user_data["cover_letter_template"] = cover_letter

        click.echo("✅ Application responses collected\n")

    async def _collect_notion_config(self) -> None:
        """Collect Notion integration configuration."""
        click.echo("📊 Notion Integration Setup")
        click.echo("-" * 50)

        click.echo("To track your job applications, you'll need a Notion API key.")
        click.echo("Visit: https://www.notion.so/my-integrations to create one.")

        # Notion API key
        while True:
            api_key = click.prompt("Notion API Key", hide_input=True)
            valid, msg = self.validator.validate_notion_api_key(api_key)
            if valid:
                self.config_data["notion_config"] = {"api_key": api_key}
                break
            click.echo(f"❌ {msg}")

        # Database name
        database_name = click.prompt("Database name for job tracking", default="Job Applications")
        self.config_data["notion_config"]["database_name"] = database_name

        click.echo("✅ Notion configuration collected\n")

    async def _collect_ai_config(self) -> None:
        """Collect AI/LLM configuration."""
        click.echo("🤖 AI Configuration")
        click.echo("-" * 50)

        click.echo("AI will help answer novel application questions.")

        # AI provider choice
        providers = ["OpenAI (GPT-4)", "Anthropic (Claude)", "Skip AI setup"]
        click.echo("Choose AI provider:")
        for i, provider in enumerate(providers, 1):
            click.echo(f"{i}. {provider}")

        while True:
            try:
                choice = int(click.prompt("Select option (1-3)"))
                if choice == 1:
                    api_key = click.prompt("OpenAI API Key", hide_input=True)
                    self.config_data["ai_config"] = {
                        "provider": "openai",
                        "api_key": api_key,
                        "model": "gpt-4-turbo-preview"
                    }
                    break
                elif choice == 2:
                    api_key = click.prompt("Anthropic API Key", hide_input=True)
                    self.config_data["ai_config"] = {
                        "provider": "anthropic",
                        "api_key": api_key,
                        "model": "claude-3-sonnet-20240229"
                    }
                    break
                elif choice == 3:
                    self.config_data["ai_config"] = {"provider": "none"}
                    break
                else:
                    click.echo("❌ Please select a number between 1 and 3")
            except ValueError:
                click.echo("❌ Please enter a valid number")

        click.echo("✅ AI configuration collected\n")

    def _validate_setup_data(self) -> None:
        """Validate all collected setup data."""
        click.echo("🔍 Validating setup data...")

        # Validate user data
        validation_results = self.validator.validate_user_data(self.user_data)

        errors = []
        for field, result in validation_results.items():
            if "Invalid" in result or "failed" in result.lower():
                errors.append(f"{field}: {result}")

        if errors:
            click.echo("❌ Validation errors found:")
            for error in errors:
                click.echo(f"  - {error}")
            raise ValueError("Setup data validation failed")

        click.echo("✅ All data validated successfully")

    def _save_setup_data(self) -> None:
        """Save all setup data to encrypted storage."""
        click.echo("💾 Saving configuration...")

        # Add metadata
        import datetime
        self.config_data.update({
            "setup_completed": True,
            "created_at": datetime.datetime.now().isoformat(),
            "last_updated": datetime.datetime.now().isoformat()
        })

        # Save user data and configuration
        self.config_manager.save_user_data(self.user_data)
        self.config_manager.save_config(self.config_data)

        click.echo("✅ Configuration saved successfully")
