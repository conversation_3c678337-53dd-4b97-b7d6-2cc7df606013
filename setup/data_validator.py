"""
Data validation and format verification utilities.
"""

import re
import os
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from loguru import logger


class DataValidator:
    """Validates user input data and file formats."""
    
    @staticmethod
    def validate_email(email: str) -> Tuple[bool, str]:
        """Validate email address format."""
        if not email:
            return False, "Email is required"
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(pattern, email):
            return True, "Valid email"
        return False, "Invalid email format"
    
    @staticmethod
    def validate_phone(phone: str) -> Tuple[bool, str]:
        """Validate phone number format."""
        if not phone:
            return False, "Phone number is required"
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        
        # Check if it's a valid US phone number (10 or 11 digits)
        if len(digits_only) == 10:
            return True, "Valid phone number"
        elif len(digits_only) == 11 and digits_only.startswith('1'):
            return True, "Valid phone number"
        else:
            return False, "Invalid phone number format (use US format)"
    
    @staticmethod
    def validate_name(name: str) -> Tuple[bool, str]:
        """Validate name format."""
        if not name or len(name.strip()) < 2:
            return False, "Name must be at least 2 characters"
        
        if len(name) > 100:
            return False, "Name is too long (max 100 characters)"
        
        # Check for basic name pattern (letters, spaces, hyphens, apostrophes)
        pattern = r"^[a-zA-Z\s\-'\.]+$"
        if re.match(pattern, name):
            return True, "Valid name"
        return False, "Name contains invalid characters"
    
    @staticmethod
    def validate_resume_file(file_path: str) -> Tuple[bool, str]:
        """Validate resume file exists and is PDF format."""
        if not file_path:
            return False, "Resume file path is required"
        
        path = Path(file_path)
        
        if not path.exists():
            return False, f"File does not exist: {file_path}"
        
        if not path.is_file():
            return False, f"Path is not a file: {file_path}"
        
        if path.suffix.lower() != '.pdf':
            return False, "Resume must be a PDF file"
        
        # Check file size (max 10MB)
        file_size = path.stat().st_size
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            return False, f"File too large (max 10MB): {file_size / 1024 / 1024:.1f}MB"
        
        return True, "Valid resume file"
    
    @staticmethod
    def validate_url(url: str) -> Tuple[bool, str]:
        """Validate URL format."""
        if not url:
            return True, "URL is optional"  # URLs are often optional
        
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        if re.match(pattern, url):
            return True, "Valid URL"
        return False, "Invalid URL format (must start with http:// or https://)"
    
    @staticmethod
    def validate_linkedin_url(url: str) -> Tuple[bool, str]:
        """Validate LinkedIn profile URL."""
        if not url:
            return False, "LinkedIn URL is required"
        
        pattern = r'^https://www\.linkedin\.com/in/[a-zA-Z0-9\-]+/?$'
        if re.match(pattern, url):
            return True, "Valid LinkedIn URL"
        return False, "Invalid LinkedIn URL format (should be https://www.linkedin.com/in/username)"
    
    @staticmethod
    def validate_github_url(url: str) -> Tuple[bool, str]:
        """Validate GitHub profile URL."""
        if not url:
            return True, "GitHub URL is optional"
        
        pattern = r'^https://github\.com/[a-zA-Z0-9\-]+/?$'
        if re.match(pattern, url):
            return True, "Valid GitHub URL"
        return False, "Invalid GitHub URL format (should be https://github.com/username)"
    
    @staticmethod
    def validate_notion_api_key(api_key: str) -> Tuple[bool, str]:
        """Validate Notion API key format."""
        if not api_key:
            return False, "Notion API key is required"
        
        # Notion API keys start with "secret_" and are followed by alphanumeric characters
        pattern = r'^secret_[a-zA-Z0-9]{43}$'
        if re.match(pattern, api_key):
            return True, "Valid Notion API key format"
        return False, "Invalid Notion API key format (should start with 'secret_')"
    
    @staticmethod
    def validate_job_keywords(keywords: List[str]) -> Tuple[bool, str]:
        """Validate job search keywords."""
        if not keywords:
            return False, "At least one job keyword is required"
        
        if len(keywords) > 20:
            return False, "Too many keywords (max 20)"
        
        for keyword in keywords:
            if not keyword or len(keyword.strip()) < 2:
                return False, f"Invalid keyword: '{keyword}' (min 2 characters)"
            if len(keyword) > 100:
                return False, f"Keyword too long: '{keyword}' (max 100 characters)"
        
        return True, f"Valid keywords ({len(keywords)} keywords)"
    
    @staticmethod
    def validate_locations(locations: List[str]) -> Tuple[bool, str]:
        """Validate job search locations."""
        if not locations:
            return False, "At least one location is required"
        
        if len(locations) > 15:
            return False, "Too many locations (max 15)"
        
        for location in locations:
            if not location or len(location.strip()) < 2:
                return False, f"Invalid location: '{location}' (min 2 characters)"
            if len(location) > 100:
                return False, f"Location too long: '{location}' (max 100 characters)"
        
        return True, f"Valid locations ({len(locations)} locations)"
    
    @staticmethod
    def validate_salary_range(min_salary: Optional[int], max_salary: Optional[int]) -> Tuple[bool, str]:
        """Validate salary range."""
        if min_salary is not None and min_salary < 0:
            return False, "Minimum salary cannot be negative"
        
        if max_salary is not None and max_salary < 0:
            return False, "Maximum salary cannot be negative"
        
        if min_salary is not None and max_salary is not None:
            if min_salary > max_salary:
                return False, "Minimum salary cannot be greater than maximum salary"
        
        return True, "Valid salary range"
    
    @staticmethod
    def validate_work_authorization(status: str) -> Tuple[bool, str]:
        """Validate work authorization status."""
        valid_statuses = [
            "US Citizen",
            "Permanent Resident",
            "H1B Visa",
            "F1 Visa (OPT)",
            "Other Visa",
            "Require Sponsorship"
        ]
        
        if status in valid_statuses:
            return True, "Valid work authorization status"
        return False, f"Invalid work authorization status. Must be one of: {', '.join(valid_statuses)}"
    
    @classmethod
    def validate_user_data(cls, user_data: Dict[str, Any]) -> Dict[str, str]:
        """Validate all user data and return validation results."""
        results = {}
        
        # Required fields
        name_valid, name_msg = cls.validate_name(user_data.get("name", ""))
        results["name"] = name_msg
        
        email_valid, email_msg = cls.validate_email(user_data.get("email", ""))
        results["email"] = email_msg
        
        phone_valid, phone_msg = cls.validate_phone(user_data.get("phone", ""))
        results["phone"] = phone_msg
        
        resume_valid, resume_msg = cls.validate_resume_file(user_data.get("resume_path", ""))
        results["resume"] = resume_msg
        
        linkedin_valid, linkedin_msg = cls.validate_linkedin_url(user_data.get("linkedin_url", ""))
        results["linkedin"] = linkedin_msg
        
        # Optional fields
        github_url = user_data.get("github_url", "")
        if github_url:
            github_valid, github_msg = cls.validate_github_url(github_url)
            results["github"] = github_msg
        
        # Work authorization
        work_auth = user_data.get("work_authorization", "")
        if work_auth:
            auth_valid, auth_msg = cls.validate_work_authorization(work_auth)
            results["work_authorization"] = auth_msg
        
        return results
