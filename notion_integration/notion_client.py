"""
Notion API client for job application tracking.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, date
from loguru import logger

try:
    from notion_client import Client
    from notion_client.errors import APIResponseError
except ImportError:
    logger.warning("notion-client not installed. Install with: pip install notion-client")
    Client = None
    APIResponseError = Exception

from config.config_manager import ConfigManager
from config.settings import NOTION_DATABASE_SCHEMA
from utils.logger import log_notion_operation


class NotionClient:
    """Handles Notion API operations for job tracking."""
    
    def __init__(self):
        """Initialize Notion client."""
        self.client = None
        self.database_id = None
        self.config_manager = ConfigManager()
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize Notion client with API key."""
        try:
            if Client is None:
                raise ImportError("notion-client library not available")
            
            notion_config = self.config_manager.get_notion_config()
            api_key = notion_config.get("api_key")
            
            if not api_key:
                logger.warning("Notion API key not configured")
                return
            
            self.client = Client(auth=api_key)
            logger.info("Notion client initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Notion client: {e}")
    
    def test_connection(self) -> bool:
        """Test Notion API connection."""
        try:
            if not self.client:
                return False
            
            # Try to list users (basic API test)
            self.client.users.list()
            log_notion_operation("TEST_CONNECTION", success=True)
            return True
            
        except Exception as e:
            logger.error(f"Notion connection test failed: {e}")
            log_notion_operation("TEST_CONNECTION", success=False)
            return False
    
    async def create_database(self, parent_page_id: str, database_name: str = "Job Applications") -> Optional[str]:
        """Create job tracking database."""
        try:
            if not self.client:
                raise ValueError("Notion client not initialized")
            
            # Database properties based on schema
            properties = {}
            for field_name, field_config in NOTION_DATABASE_SCHEMA.items():
                if field_config["type"] == "title":
                    properties[field_name] = {"title": {}}
                elif field_config["type"] == "rich_text":
                    properties[field_name] = {"rich_text": {}}
                elif field_config["type"] == "select":
                    properties[field_name] = {
                        "select": {
                            "options": field_config.get("options", [])
                        }
                    }
                elif field_config["type"] == "date":
                    properties[field_name] = {"date": {}}
                elif field_config["type"] == "url":
                    properties[field_name] = {"url": {}}
            
            # Create database
            response = self.client.databases.create(
                parent={"page_id": parent_page_id},
                title=[{"type": "text", "text": {"content": database_name}}],
                properties=properties
            )
            
            self.database_id = response["id"]
            
            # Save database ID to config
            notion_config = self.config_manager.get_notion_config()
            notion_config["database_id"] = self.database_id
            self.config_manager.update_config_section("notion_config", notion_config)
            
            logger.info(f"Database created: {database_name}")
            log_notion_operation("CREATE_DATABASE", database_name, success=True)
            
            return self.database_id
            
        except Exception as e:
            logger.error(f"Failed to create database: {e}")
            log_notion_operation("CREATE_DATABASE", str(e), success=False)
            return None
    
    def get_database_id(self) -> Optional[str]:
        """Get database ID from config."""
        if self.database_id:
            return self.database_id
        
        notion_config = self.config_manager.get_notion_config()
        self.database_id = notion_config.get("database_id")
        return self.database_id
    
    async def add_job_application(self, job_data: Dict[str, Any]) -> Optional[str]:
        """Add new job application to database."""
        try:
            if not self.client:
                raise ValueError("Notion client not initialized")
            
            database_id = self.get_database_id()
            if not database_id:
                raise ValueError("Database ID not configured")
            
            # Prepare properties
            properties = {
                "Job Title": {
                    "title": [{"type": "text", "text": {"content": job_data.get("title", "")}}]
                },
                "Company": {
                    "rich_text": [{"type": "text", "text": {"content": job_data.get("company", "")}}]
                },
                "Location": {
                    "rich_text": [{"type": "text", "text": {"content": job_data.get("location", "")}}]
                },
                "Application Status": {
                    "select": {"name": job_data.get("status", "Applied")}
                },
                "Source": {
                    "select": {"name": job_data.get("source", "LinkedIn")}
                },
                "Application Date": {
                    "date": {"start": job_data.get("date", datetime.now().isoformat())}
                },
                "Job URL": {
                    "url": job_data.get("url", "")
                }
            }
            
            # Add optional fields
            if job_data.get("salary"):
                properties["Salary Range"] = {
                    "rich_text": [{"type": "text", "text": {"content": job_data["salary"]}}]
                }
            
            if job_data.get("description"):
                properties["Description"] = {
                    "rich_text": [{"type": "text", "text": {"content": job_data["description"][:2000]}}]  # Limit length
                }
            
            if job_data.get("notes"):
                properties["Notes"] = {
                    "rich_text": [{"type": "text", "text": {"content": job_data["notes"]}}]
                }
            
            # Create page
            response = self.client.pages.create(
                parent={"database_id": database_id},
                properties=properties
            )
            
            page_id = response["id"]
            logger.info(f"Job application added: {job_data.get('title')} at {job_data.get('company')}")
            log_notion_operation("ADD_APPLICATION", f"{job_data.get('company')} - {job_data.get('title')}", success=True)
            
            return page_id
            
        except Exception as e:
            logger.error(f"Failed to add job application: {e}")
            log_notion_operation("ADD_APPLICATION", str(e), success=False)
            return None
    
    async def update_application_status(self, page_id: str, status: str, notes: str = "") -> bool:
        """Update application status."""
        try:
            if not self.client:
                raise ValueError("Notion client not initialized")
            
            properties = {
                "Application Status": {
                    "select": {"name": status}
                }
            }
            
            if notes:
                properties["Notes"] = {
                    "rich_text": [{"type": "text", "text": {"content": notes}}]
                }
            
            self.client.pages.update(
                page_id=page_id,
                properties=properties
            )
            
            logger.info(f"Application status updated: {status}")
            log_notion_operation("UPDATE_STATUS", f"{page_id}: {status}", success=True)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update application status: {e}")
            log_notion_operation("UPDATE_STATUS", str(e), success=False)
            return False
    
    async def get_applications(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get list of job applications."""
        try:
            if not self.client:
                raise ValueError("Notion client not initialized")
            
            database_id = self.get_database_id()
            if not database_id:
                raise ValueError("Database ID not configured")
            
            response = self.client.databases.query(
                database_id=database_id,
                page_size=limit,
                sorts=[
                    {
                        "property": "Application Date",
                        "direction": "descending"
                    }
                ]
            )
            
            applications = []
            for page in response["results"]:
                app_data = self._parse_page_properties(page["properties"])
                app_data["page_id"] = page["id"]
                applications.append(app_data)
            
            log_notion_operation("GET_APPLICATIONS", f"Retrieved {len(applications)} applications", success=True)
            return applications
            
        except Exception as e:
            logger.error(f"Failed to get applications: {e}")
            log_notion_operation("GET_APPLICATIONS", str(e), success=False)
            return []
    
    async def check_duplicate_application(self, company: str, job_title: str) -> bool:
        """Check if application already exists."""
        try:
            if not self.client:
                return False
            
            database_id = self.get_database_id()
            if not database_id:
                return False
            
            # Query for existing application
            response = self.client.databases.query(
                database_id=database_id,
                filter={
                    "and": [
                        {
                            "property": "Company",
                            "rich_text": {
                                "contains": company
                            }
                        },
                        {
                            "property": "Job Title",
                            "title": {
                                "contains": job_title
                            }
                        }
                    ]
                }
            )
            
            exists = len(response["results"]) > 0
            if exists:
                logger.info(f"Duplicate application found: {company} - {job_title}")
            
            return exists
            
        except Exception as e:
            logger.error(f"Failed to check duplicate: {e}")
            return False
    
    def _parse_page_properties(self, properties: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Notion page properties to simple dict."""
        parsed = {}
        
        for prop_name, prop_data in properties.items():
            prop_type = prop_data["type"]
            
            if prop_type == "title" and prop_data["title"]:
                parsed[prop_name] = prop_data["title"][0]["text"]["content"]
            elif prop_type == "rich_text" and prop_data["rich_text"]:
                parsed[prop_name] = prop_data["rich_text"][0]["text"]["content"]
            elif prop_type == "select" and prop_data["select"]:
                parsed[prop_name] = prop_data["select"]["name"]
            elif prop_type == "date" and prop_data["date"]:
                parsed[prop_name] = prop_data["date"]["start"]
            elif prop_type == "url":
                parsed[prop_name] = prop_data["url"]
            else:
                parsed[prop_name] = None
        
        return parsed
    
    async def get_weekly_summary(self) -> Dict[str, Any]:
        """Get weekly application summary."""
        try:
            # Get applications from last 7 days
            from datetime import timedelta
            week_ago = (datetime.now() - timedelta(days=7)).isoformat()
            
            database_id = self.get_database_id()
            if not database_id:
                return {}
            
            response = self.client.databases.query(
                database_id=database_id,
                filter={
                    "property": "Application Date",
                    "date": {
                        "after": week_ago
                    }
                }
            )
            
            applications = response["results"]
            
            # Calculate summary stats
            total_applications = len(applications)
            status_counts = {}
            source_counts = {}
            
            for app in applications:
                props = app["properties"]
                
                # Count by status
                status = props.get("Application Status", {}).get("select", {}).get("name", "Unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Count by source
                source = props.get("Source", {}).get("select", {}).get("name", "Unknown")
                source_counts[source] = source_counts.get(source, 0) + 1
            
            summary = {
                "total_applications": total_applications,
                "status_breakdown": status_counts,
                "source_breakdown": source_counts,
                "period": "Last 7 days"
            }
            
            log_notion_operation("WEEKLY_SUMMARY", f"Generated summary: {total_applications} applications", success=True)
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate weekly summary: {e}")
            log_notion_operation("WEEKLY_SUMMARY", str(e), success=False)
            return {}
