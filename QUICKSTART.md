# Quick Start Guide

Get your job application automation system up and running in 5 minutes!

## Prerequisites

- Python 3.9+ (Python 3.12+ recommended)
- macOS, Linux, or Windows
- Internet connection

## Installation

### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone https://github.com/HemanthKiranPolu/JOB_Auto_Appllier.git
cd JOB_Auto_Appllier

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
python -m playwright install chromium

# Test installation
python test_installation.py
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit with your API keys
nano .env
```

**Required API Keys:**
- `NOTION_API_KEY`: Get from https://www.notion.so/my-integrations
- `OPENAI_API_KEY` or `ANTHROPIC_API_KEY`: For AI responses (optional)

### 3. Run Demo

```bash
# Test all components
python demo.py
```

## First Run

### 1. Initial Setup

```bash
python main.py setup
```

This will collect:
- Personal information (name, email, phone, address)
- Resume file (PDF format)
- Professional profiles (LinkedIn, GitHub)
- Job search preferences
- Standard application responses

### 2. Test Run

```bash
# Dry run (no actual applications)
python main.py apply --dry-run --max-applications 5
```

### 3. Live Run

```bash
# Apply to jobs (start small)
python main.py apply --max-applications 3
```

## Daily Automation

### Schedule Daily Runs

```bash
# Schedule for 8:00 AM daily
python main.py schedule --time 08:00
```

### Check Status

```bash
# View system status
python main.py status

# View configuration
python main.py config
```

## Notion Setup

### 1. Create Integration

1. Go to https://www.notion.so/my-integrations
2. Click "New integration"
3. Name it "Job Application Agent"
4. Copy the API key to your `.env` file

### 2. Create Database

The system will automatically create a job tracking database, or you can:

1. Create a new page in Notion
2. Add a database with these columns:
   - Job Title (Title)
   - Company (Text)
   - Location (Text)
   - Application Status (Select)
   - Source (Select)
   - Application Date (Date)
   - Job URL (URL)
   - Notes (Text)

### 3. Share with Integration

1. Open your database page
2. Click "Share" → "Invite"
3. Add your integration

## Troubleshooting

### Common Issues

**"Python version not supported"**
- Install Python 3.12+ or use Python 3.9+ with limited features

**"Browser automation fails"**
```bash
# Reinstall Playwright
python -m playwright install --force chromium
```

**"Notion connection failed"**
- Check API key in `.env`
- Verify integration has database access
- Test connection: `python -c "from notion_integration.notion_client import NotionClient; print(NotionClient().test_connection())"`

**"LinkedIn login issues"**
- LinkedIn may require manual verification
- Use 2FA if enabled
- Check for CAPTCHA in screenshots

### Debug Mode

```bash
# Run with detailed logging
python main.py apply --debug --dry-run
```

### Logs

Check logs in the `logs/` directory:
- `job_agent.log`: General application logs
- `applications.log`: Application attempts
- `errors.log`: Error messages

## Configuration

### Job Preferences

Edit during setup or modify in Notion:
- Keywords: "Software Engineer Intern", "Cloud Engineer"
- Locations: "San Francisco", "Remote", "New York"
- Experience levels: "Internship", "Entry level"

### Application Limits

Default limits (configurable):
- Max daily applications: 50
- Max per company: 3
- Delay between applications: 30-120 seconds

### Browser Settings

- Headless mode: Enabled for automation
- Screenshots: Saved for debugging
- User agent: Randomized for each session

## Best Practices

### Security
- Use strong master password for encryption
- Keep API keys secure
- Regular backup of configuration

### Job Applications
- Start with dry runs
- Monitor application success rate
- Review and update responses regularly
- Check Notion database daily

### Maintenance
- Update job preferences monthly
- Clean old logs weekly
- Test system after updates

## Support

### Getting Help

1. Check logs for error details
2. Run diagnostic tests: `python test_installation.py`
3. Review troubleshooting section
4. Create GitHub issue with logs

### Useful Commands

```bash
# System status
python main.py status

# Reset configuration
python -c "from config.config_manager import ConfigManager; ConfigManager().reset_configuration()"

# Test browser
python -c "import asyncio; from automation.browser_manager import BrowserManager; asyncio.run(BrowserManager().start_browser())"

# Test Notion
python -c "from notion_integration.notion_client import NotionClient; print(NotionClient().test_connection())"
```

## Next Steps

1. **Customize**: Adjust job preferences and application responses
2. **Monitor**: Check Notion database for application status
3. **Optimize**: Fine-tune keywords and filters based on results
4. **Scale**: Increase daily application limits gradually
5. **Maintain**: Regular updates and monitoring

---

**Happy job hunting! 🚀**

For detailed documentation, see [README.md](README.md)
