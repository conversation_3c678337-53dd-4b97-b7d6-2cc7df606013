#!/usr/bin/env python3
"""
Sample configuration examples for the Job Application Automation System.
This file shows example configurations for different use cases.
"""

# Example 1: Software Engineering Intern
SOFTWARE_ENGINEER_INTERN_CONFIG = {
    "job_preferences": {
        "keywords": [
            "Software Engineer Intern",
            "Software Engineering Intern",
            "Backend Engineer Intern",
            "Frontend Engineer Intern",
            "Full Stack Engineer Intern",
            "Python Developer Intern",
            "Java Developer Intern",
            "Web Developer Intern"
        ],
        "locations": [
            "San Francisco, CA",
            "San Jose, CA",
            "Mountain View, CA",
            "Palo Alto, CA",
            "Seattle, WA",
            "New York, NY",
            "Remote",
            "United States"
        ],
        "experience_levels": ["Internship", "Entry level"],
        "job_types": ["Internship", "Full-time"],
        "remote_preference": "Remote, Hybrid, On-site",
        "salary_min": 25,  # per hour for internships
        "salary_max": 50
    },
    "application_settings": {
        "max_daily_applications": 30,
        "max_per_company": 2,
        "min_delay_between_applications": 45,
        "max_delay_between_applications": 120
    }
}

# Example 2: Cloud Engineering Focus
CLOUD_ENGINEER_CONFIG = {
    "job_preferences": {
        "keywords": [
            "Cloud Engineer Intern",
            "DevOps Engineer Intern",
            "AWS Engineer Intern",
            "Azure Engineer Intern",
            "GCP Engineer Intern",
            "Infrastructure Engineer Intern",
            "Site Reliability Engineer Intern",
            "Platform Engineer Intern"
        ],
        "locations": [
            "San Francisco, CA",
            "Austin, TX",
            "Seattle, WA",
            "Denver, CO",
            "Remote"
        ],
        "experience_levels": ["Internship", "Entry level"],
        "job_types": ["Internship", "Full-time"],
        "remote_preference": "Remote, Hybrid",
        "salary_min": 30,
        "salary_max": 60
    },
    "application_settings": {
        "max_daily_applications": 25,
        "max_per_company": 3,
        "min_delay_between_applications": 60,
        "max_delay_between_applications": 180
    }
}

# Example 3: Data Science/ML Focus
DATA_SCIENCE_CONFIG = {
    "job_preferences": {
        "keywords": [
            "Data Science Intern",
            "Machine Learning Intern",
            "AI Engineer Intern",
            "Data Analyst Intern",
            "Research Intern",
            "ML Engineer Intern",
            "Data Engineer Intern",
            "Analytics Intern"
        ],
        "locations": [
            "San Francisco, CA",
            "New York, NY",
            "Boston, MA",
            "Chicago, IL",
            "Remote"
        ],
        "experience_levels": ["Internship", "Entry level"],
        "job_types": ["Internship", "Full-time"],
        "remote_preference": "Remote, Hybrid, On-site",
        "salary_min": 25,
        "salary_max": 55
    },
    "application_settings": {
        "max_daily_applications": 20,
        "max_per_company": 2,
        "min_delay_between_applications": 90,
        "max_delay_between_applications": 240
    }
}

# Example User Data Template
SAMPLE_USER_DATA = {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "(*************",
    "address": {
        "street": "123 University Ave",
        "city": "College Town",
        "state": "CA",
        "zip_code": "12345",
        "country": "United States"
    },
    "linkedin_url": "https://www.linkedin.com/in/johndoe",
    "github_url": "https://github.com/johndoe",
    "portfolio_url": "https://johndoe.dev",
    "education": {
        "degree": "Bachelor of Science in Computer Science",
        "university": "University of Technology",
        "graduation_date": "05/2024",
        "gpa": "3.8"
    },
    "skills": [
        "Python", "Java", "JavaScript", "React", "Node.js",
        "SQL", "Git", "Docker", "AWS", "Linux"
    ],
    "work_authorization": "US Citizen",
    "requires_sponsorship": False,
    "availability": {
        "start_date": "06/01/2024",
        "notice_period": "2 weeks"
    },
    "cover_letter_template": "I am excited to apply for this position as it aligns perfectly with my passion for technology and my academic background in computer science. I am eager to contribute to your team and learn from experienced professionals."
}

# Example Standard Application Responses
STANDARD_RESPONSES = {
    "work_authorization": {
        "US Citizen": "Yes, I am authorized to work in the US",
        "Permanent Resident": "Yes, I am a permanent resident",
        "F1 Visa (OPT)": "Yes, I am on F1 visa with OPT eligibility",
        "Require Sponsorship": "I will require sponsorship"
    },
    "graduation_date": "May 2024",
    "gpa": "3.8/4.0",
    "availability": "I am available to start immediately after graduation in May 2024",
    "salary_expectations": "I am open to discussing compensation based on the role and responsibilities",
    "why_interested": "I am passionate about technology and excited about the opportunity to contribute to innovative projects while learning from experienced professionals",
    "strengths": "Strong problem-solving skills, quick learner, collaborative team player, and passionate about clean, efficient code",
    "career_goals": "To develop expertise in software engineering and contribute to meaningful projects that make a positive impact"
}

# Example Notion Database Configuration
NOTION_DATABASE_CONFIG = {
    "database_name": "Job Applications 2024",
    "properties": {
        "Job Title": {"type": "title"},
        "Company": {"type": "rich_text"},
        "Location": {"type": "rich_text"},
        "Salary Range": {"type": "rich_text"},
        "Application Status": {
            "type": "select",
            "options": [
                {"name": "Applied", "color": "blue"},
                {"name": "Phone Screen", "color": "yellow"},
                {"name": "Technical Interview", "color": "orange"},
                {"name": "Final Interview", "color": "purple"},
                {"name": "Offer Received", "color": "green"},
                {"name": "Rejected", "color": "red"},
                {"name": "Withdrawn", "color": "gray"},
                {"name": "Manual Action Needed", "color": "pink"}
            ]
        },
        "Source": {
            "type": "select",
            "options": [
                {"name": "LinkedIn", "color": "blue"},
                {"name": "Indeed", "color": "green"},
                {"name": "Company Website", "color": "yellow"},
                {"name": "Referral", "color": "purple"},
                {"name": "University Portal", "color": "orange"}
            ]
        },
        "Application Date": {"type": "date"},
        "Job URL": {"type": "url"},
        "Description": {"type": "rich_text"},
        "Notes": {"type": "rich_text"},
        "Follow Up Date": {"type": "date"},
        "Priority": {
            "type": "select",
            "options": [
                {"name": "High", "color": "red"},
                {"name": "Medium", "color": "yellow"},
                {"name": "Low", "color": "gray"}
            ]
        }
    }
}

# Example Browser Configuration
BROWSER_CONFIG_EXAMPLES = {
    "development": {
        "headless": False,
        "window_size": (1920, 1080),
        "timeout": 30,
        "implicit_wait": 10,
        "page_load_timeout": 60,
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    },
    "production": {
        "headless": True,
        "window_size": (1920, 1080),
        "timeout": 45,
        "implicit_wait": 15,
        "page_load_timeout": 90,
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    },
    "conservative": {
        "headless": True,
        "window_size": (1366, 768),
        "timeout": 60,
        "implicit_wait": 20,
        "page_load_timeout": 120,
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
}

# Example AI Configuration
AI_CONFIG_EXAMPLES = {
    "openai": {
        "provider": "openai",
        "model": "gpt-4-turbo-preview",
        "max_tokens": 500,
        "temperature": 0.7,
        "timeout": 30
    },
    "anthropic": {
        "provider": "anthropic",
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 500,
        "temperature": 0.7,
        "timeout": 30
    },
    "conservative": {
        "provider": "openai",
        "model": "gpt-3.5-turbo",
        "max_tokens": 300,
        "temperature": 0.5,
        "timeout": 45
    }
}

# Example Scheduling Configuration
SCHEDULING_EXAMPLES = {
    "daily_morning": {
        "time": "08:00",
        "max_applications": 25,
        "enabled_portals": ["linkedin", "indeed"]
    },
    "daily_evening": {
        "time": "18:00",
        "max_applications": 15,
        "enabled_portals": ["linkedin"]
    },
    "weekdays_only": {
        "time": "09:00",
        "max_applications": 30,
        "enabled_portals": ["linkedin", "indeed"],
        "skip_weekends": True
    }
}

def get_config_for_role(role_type: str):
    """Get configuration for specific role type."""
    configs = {
        "software_engineer": SOFTWARE_ENGINEER_INTERN_CONFIG,
        "cloud_engineer": CLOUD_ENGINEER_CONFIG,
        "data_science": DATA_SCIENCE_CONFIG
    }
    return configs.get(role_type, SOFTWARE_ENGINEER_INTERN_CONFIG)

def get_user_data_template():
    """Get user data template."""
    return SAMPLE_USER_DATA.copy()

def get_standard_responses():
    """Get standard application responses."""
    return STANDARD_RESPONSES.copy()

if __name__ == "__main__":
    print("Sample Configuration Examples")
    print("=" * 40)
    print("Available configurations:")
    print("- SOFTWARE_ENGINEER_INTERN_CONFIG")
    print("- CLOUD_ENGINEER_CONFIG") 
    print("- DATA_SCIENCE_CONFIG")
    print("- SAMPLE_USER_DATA")
    print("- STANDARD_RESPONSES")
    print("- NOTION_DATABASE_CONFIG")
    print("- BROWSER_CONFIG_EXAMPLES")
    print("- AI_CONFIG_EXAMPLES")
    print("- SCHEDULING_EXAMPLES")
