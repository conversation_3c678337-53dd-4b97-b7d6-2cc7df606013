#!/bin/bash

# Job Application Automation System - Installation Script
# This script automates the installation process

set -e  # Exit on any error

echo "🚀 Job Application Automation System - Installation"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Python is installed
check_python() {
    print_info "Checking Python installation..."
    
    if command -v python3.12 &> /dev/null; then
        PYTHON_CMD="python3.12"
        print_status "Found Python 3.12"
    elif command -v python3.11 &> /dev/null; then
        PYTHON_CMD="python3.11"
        print_status "Found Python 3.11"
    elif command -v python3.10 &> /dev/null; then
        PYTHON_CMD="python3.10"
        print_warning "Found Python 3.10 (some features may be limited)"
    elif command -v python3.9 &> /dev/null; then
        PYTHON_CMD="python3.9"
        print_warning "Found Python 3.9 (some features may be limited)"
    elif command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        VERSION=$(python3 --version | cut -d' ' -f2)
        print_warning "Found Python $VERSION"
    else
        print_error "Python 3.9+ is required but not found"
        echo "Please install Python 3.12+ from https://python.org"
        exit 1
    fi
}

# Install Python dependencies
install_dependencies() {
    print_info "Installing Python dependencies..."
    
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 not found. Please install pip3"
        exit 1
    fi
    
    pip3 install -r requirements.txt
    print_status "Python dependencies installed"
}

# Install Playwright browsers
install_playwright() {
    print_info "Installing Playwright browsers..."
    
    $PYTHON_CMD -m playwright install chromium
    print_status "Playwright browsers installed"
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    mkdir -p data logs screenshots
    print_status "Directories created"
}

# Copy environment file
setup_environment() {
    print_info "Setting up environment file..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_status "Environment file created (.env)"
        print_warning "Please edit .env file with your API keys"
    else
        print_warning ".env file already exists, skipping"
    fi
}

# Run installation test
run_tests() {
    print_info "Running installation tests..."
    
    if $PYTHON_CMD test_installation.py; then
        print_status "All installation tests passed!"
    else
        print_error "Some installation tests failed"
        echo "Please check the output above for details"
        exit 1
    fi
}

# Run demo
run_demo() {
    print_info "Running system demo..."
    
    if $PYTHON_CMD demo.py; then
        print_status "Demo completed successfully!"
    else
        print_warning "Demo had some issues, but installation may still be OK"
    fi
}

# Main installation process
main() {
    echo
    print_info "Starting installation process..."
    echo
    
    check_python
    echo
    
    install_dependencies
    echo
    
    install_playwright
    echo
    
    create_directories
    echo
    
    setup_environment
    echo
    
    run_tests
    echo
    
    print_info "Running demo (optional)..."
    if read -p "Run demo? (y/N): " -n 1 -r; then
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            run_demo
        fi
    fi
    echo
    
    print_status "Installation completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Edit .env file with your API keys:"
    echo "   - NOTION_API_KEY (required)"
    echo "   - OPENAI_API_KEY or ANTHROPIC_API_KEY (optional)"
    echo "2. Run setup: $PYTHON_CMD main.py setup"
    echo "3. Test with: $PYTHON_CMD main.py apply --dry-run"
    echo
    echo "For detailed instructions, see QUICKSTART.md"
}

# Run main function
main "$@"
