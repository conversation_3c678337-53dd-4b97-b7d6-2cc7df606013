#!/usr/bin/env python3
"""
Management utility for the Job Application Automation System.
"""

import sys
import asyncio
import click
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.config_manager import ConfigManager
from notion_integration.notion_client import NotionClient
from utils.logger import setup_logging


@click.group()
def cli():
    """Job Application Automation System - Management Utilities"""
    setup_logging()


@cli.command()
def status():
    """Show detailed system status."""
    click.echo("📊 System Status Report")
    click.echo("=" * 50)
    
    try:
        config_manager = ConfigManager()
        
        # Setup status
        if config_manager.is_setup_complete():
            click.echo("✅ Setup: Complete")
            
            # User info
            user_data = config_manager.get_user_data()
            click.echo(f"👤 User: {user_data.get('name', 'Unknown')}")
            click.echo(f"📧 Email: {user_data.get('email', 'Unknown')}")
            click.echo(f"📄 Resume: {'✅' if user_data.get('resume_path') else '❌'}")
            
            # Job preferences
            job_prefs = config_manager.get_job_preferences()
            click.echo(f"🎯 Keywords: {len(job_prefs.get('keywords', []))}")
            click.echo(f"📍 Locations: {len(job_prefs.get('locations', []))}")
            
            # Notion status
            notion_config = config_manager.get_notion_config()
            if notion_config.get('api_key'):
                notion_client = NotionClient()
                if notion_client.test_connection():
                    click.echo("✅ Notion: Connected")
                else:
                    click.echo("❌ Notion: Connection failed")
            else:
                click.echo("⚠️  Notion: Not configured")
            
            # AI status
            ai_config = config_manager.get_ai_config()
            provider = ai_config.get('provider', 'none')
            if provider != 'none':
                click.echo(f"🤖 AI: {provider.title()}")
            else:
                click.echo("⚠️  AI: Not configured")
                
        else:
            click.echo("❌ Setup: Not completed")
            click.echo("Run 'python main.py setup' to get started.")
        
        # Check logs
        logs_dir = Path("logs")
        if logs_dir.exists():
            log_files = list(logs_dir.glob("*.log"))
            click.echo(f"📝 Log files: {len(log_files)}")
        
        # Check data directory
        data_dir = Path("data")
        if data_dir.exists():
            data_files = list(data_dir.glob("*"))
            click.echo(f"💾 Data files: {len(data_files)}")
            
    except Exception as e:
        click.echo(f"❌ Status check failed: {e}")


@cli.command()
@click.option('--days', default=7, help='Number of days to show')
def applications(days):
    """Show recent job applications."""
    click.echo(f"📋 Applications (Last {days} days)")
    click.echo("=" * 50)
    
    try:
        notion_client = NotionClient()
        
        if not notion_client.test_connection():
            click.echo("❌ Notion connection failed")
            return
        
        # Get recent applications
        applications = asyncio.run(notion_client.get_applications(limit=100))
        
        if not applications:
            click.echo("No applications found")
            return
        
        # Filter by date
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_apps = []
        
        for app in applications:
            app_date_str = app.get('Application Date')
            if app_date_str:
                try:
                    app_date = datetime.fromisoformat(app_date_str.replace('Z', '+00:00'))
                    if app_date.replace(tzinfo=None) >= cutoff_date:
                        recent_apps.append(app)
                except:
                    recent_apps.append(app)  # Include if date parsing fails
        
        # Display applications
        for app in recent_apps[:20]:  # Show last 20
            title = app.get('Job Title', 'Unknown')
            company = app.get('Company', 'Unknown')
            status = app.get('Application Status', 'Unknown')
            source = app.get('Source', 'Unknown')
            date = app.get('Application Date', 'Unknown')
            
            status_emoji = {
                'Applied': '✅',
                'Interview Scheduled': '📅',
                'Rejected': '❌',
                'Offer Received': '🎉',
                'Manual Action Needed': '⚠️',
                'Skipped': '⏭️'
            }.get(status, '❓')
            
            click.echo(f"{status_emoji} {title} at {company} ({source}) - {date[:10]}")
        
        # Summary
        status_counts = {}
        for app in recent_apps:
            status = app.get('Application Status', 'Unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        click.echo(f"\nSummary ({len(recent_apps)} applications):")
        for status, count in status_counts.items():
            click.echo(f"  {status}: {count}")
            
    except Exception as e:
        click.echo(f"❌ Failed to get applications: {e}")


@cli.command()
def logs():
    """Show recent log entries."""
    click.echo("📝 Recent Log Entries")
    click.echo("=" * 50)
    
    try:
        log_file = Path("logs/job_agent.log")
        if not log_file.exists():
            click.echo("No log file found")
            return
        
        # Read last 50 lines
        with open(log_file, 'r') as f:
            lines = f.readlines()
            recent_lines = lines[-50:] if len(lines) > 50 else lines
        
        for line in recent_lines:
            click.echo(line.strip())
            
    except Exception as e:
        click.echo(f"❌ Failed to read logs: {e}")


@cli.command()
@click.confirmation_option(prompt='Are you sure you want to reset all configuration?')
def reset():
    """Reset all configuration and data."""
    click.echo("🔄 Resetting configuration...")
    
    try:
        config_manager = ConfigManager()
        config_manager.reset_configuration()
        
        # Remove data files
        data_dir = Path("data")
        if data_dir.exists():
            for file in data_dir.glob("*"):
                if file.is_file():
                    file.unlink()
        
        click.echo("✅ Configuration reset successfully")
        click.echo("Run 'python main.py setup' to reconfigure")
        
    except Exception as e:
        click.echo(f"❌ Reset failed: {e}")


@cli.command()
def backup():
    """Create backup of configuration and data."""
    click.echo("💾 Creating backup...")
    
    try:
        import shutil
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = Path(f"backup_{timestamp}")
        backup_dir.mkdir()
        
        # Backup data directory
        data_dir = Path("data")
        if data_dir.exists():
            shutil.copytree(data_dir, backup_dir / "data")
        
        # Backup logs
        logs_dir = Path("logs")
        if logs_dir.exists():
            shutil.copytree(logs_dir, backup_dir / "logs")
        
        # Backup .env file
        env_file = Path(".env")
        if env_file.exists():
            shutil.copy2(env_file, backup_dir / ".env")
        
        click.echo(f"✅ Backup created: {backup_dir}")
        
    except Exception as e:
        click.echo(f"❌ Backup failed: {e}")


@cli.command()
def test():
    """Run system tests."""
    click.echo("🧪 Running system tests...")
    
    try:
        # Run installation test
        import subprocess
        result = subprocess.run([sys.executable, "test_installation.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            click.echo("✅ All tests passed!")
        else:
            click.echo("❌ Some tests failed:")
            click.echo(result.stdout)
            
    except Exception as e:
        click.echo(f"❌ Test execution failed: {e}")


@cli.command()
@click.option('--headless/--no-headless', default=True, help='Run browser in headless mode')
def test_browser():
    """Test browser automation."""
    click.echo("🌐 Testing browser automation...")
    
    async def run_browser_test():
        from automation.browser_manager import BrowserManager
        
        browser_manager = BrowserManager(headless=headless)
        await browser_manager.start_browser()
        
        # Navigate to test page
        await browser_manager.navigate_to("https://httpbin.org/html")
        
        # Take screenshot
        screenshot_path = await browser_manager.take_screenshot("browser_test.png")
        click.echo(f"Screenshot saved: {screenshot_path}")
        
        await browser_manager.close_browser()
        click.echo("✅ Browser test completed!")
    
    try:
        asyncio.run(run_browser_test())
    except Exception as e:
        click.echo(f"❌ Browser test failed: {e}")


@cli.command()
def clean():
    """Clean up old logs and temporary files."""
    click.echo("🧹 Cleaning up...")
    
    try:
        cleaned_files = 0
        
        # Clean old logs (older than 30 days)
        logs_dir = Path("logs")
        if logs_dir.exists():
            cutoff_date = datetime.now() - timedelta(days=30)
            for log_file in logs_dir.glob("*.log.*"):  # Rotated logs
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    cleaned_files += 1
        
        # Clean old screenshots
        screenshots_dir = Path("data/screenshots")
        if screenshots_dir.exists():
            cutoff_date = datetime.now() - timedelta(days=7)
            for screenshot in screenshots_dir.glob("*.png"):
                if screenshot.stat().st_mtime < cutoff_date.timestamp():
                    screenshot.unlink()
                    cleaned_files += 1
        
        # Clean temporary files
        for temp_file in Path(".").glob("*.tmp"):
            temp_file.unlink()
            cleaned_files += 1
        
        click.echo(f"✅ Cleaned {cleaned_files} files")
        
    except Exception as e:
        click.echo(f"❌ Cleanup failed: {e}")


if __name__ == '__main__':
    cli()
