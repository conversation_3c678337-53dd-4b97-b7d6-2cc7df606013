"""
LinkedIn-specific automation for job searching and applications.
"""

import asyncio
import random
from typing import Dict, Any, List, Optional
from loguru import logger

from automation.browser_manager import BrowserManager
from utils.logger import log_browser_action


class LinkedInAutomation:
    """Handles LinkedIn-specific automation tasks."""
    
    def __init__(self, browser_manager: BrowserManager):
        """Initialize LinkedIn automation."""
        self.browser = browser_manager
        self.is_logged_in = False
        
    async def login(self, email: str, password: str) -> bool:
        """Login to LinkedIn."""
        try:
            # Navigate to LinkedIn login
            await self.browser.navigate_to("https://www.linkedin.com/login")
            
            # Fill login form
            email_input = "input#username"
            password_input = "input#password"
            login_button = "button[type='submit']"
            
            await self.browser.type_text(email_input, email)
            await self.browser.type_text(password_input, password)
            await self.browser.click_element(login_button)
            
            # Wait for login to complete
            await asyncio.sleep(5)
            
            # Check if login was successful
            if await self._check_login_success():
                self.is_logged_in = True
                logger.info("LinkedIn login successful")
                return True
            else:
                logger.error("LinkedIn login failed")
                return False
                
        except Exception as e:
            logger.error(f"LinkedIn login error: {e}")
            return False
    
    async def _check_login_success(self) -> bool:
        """Check if login was successful."""
        try:
            # Look for elements that indicate successful login
            success_indicators = [
                "nav.global-nav",
                "button[aria-label='Primary Navigation']",
                "div.global-nav__content"
            ]
            
            for indicator in success_indicators:
                if await self.browser.wait_for_element(indicator, timeout=10):
                    return True
            
            # Check for error messages
            error_selectors = [
                ".alert--error",
                ".form__input--error",
                "#error-for-username",
                "#error-for-password"
            ]
            
            for error_selector in error_selectors:
                if await self.browser.is_element_visible(error_selector):
                    error_text = await self.browser.get_text(error_selector)
                    logger.error(f"Login error: {error_text}")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"Login check failed: {e}")
            return False
    
    async def search_jobs(
        self, 
        keywords: List[str], 
        locations: List[str],
        experience_levels: List[str] = None
    ) -> List[Dict[str, Any]]:
        """Search for jobs on LinkedIn."""
        all_jobs = []
        
        try:
            # Navigate to LinkedIn Jobs
            await self.browser.navigate_to("https://www.linkedin.com/jobs")
            await asyncio.sleep(3)
            
            # Handle any popups
            await self.browser.handle_popup()
            
            for keyword in keywords[:3]:  # Limit to first 3 keywords
                for location in locations[:3]:  # Limit to first 3 locations
                    try:
                        jobs = await self._search_specific_criteria(keyword, location, experience_levels)
                        all_jobs.extend(jobs)
                        
                        # Random delay between searches
                        await asyncio.sleep(random.uniform(3, 7))
                        
                    except Exception as e:
                        logger.error(f"Search failed for {keyword} in {location}: {e}")
            
            logger.info(f"Found {len(all_jobs)} jobs on LinkedIn")
            return all_jobs
            
        except Exception as e:
            logger.error(f"LinkedIn job search failed: {e}")
            return []
    
    async def _search_specific_criteria(
        self, 
        keyword: str, 
        location: str,
        experience_levels: List[str] = None
    ) -> List[Dict[str, Any]]:
        """Search for jobs with specific criteria."""
        try:
            # Fill search form
            await self._fill_search_form(keyword, location)
            
            # Apply filters
            await self._apply_search_filters(experience_levels)
            
            # Extract job listings
            jobs = await self._extract_job_listings()
            
            logger.info(f"Found {len(jobs)} jobs for '{keyword}' in '{location}'")
            return jobs
            
        except Exception as e:
            logger.error(f"Specific search failed: {e}")
            return []
    
    async def _fill_search_form(self, keyword: str, location: str) -> None:
        """Fill the LinkedIn job search form."""
        try:
            # Keywords input
            keywords_input = "input[aria-label*='Search by title']"
            if not await self.browser.wait_for_element(keywords_input, timeout=5):
                keywords_input = "input.jobs-search-box__text-input[aria-label*='title']"
            
            await self.browser.type_text(keywords_input, keyword, clear_first=True)
            await asyncio.sleep(1)
            
            # Location input
            location_input = "input[aria-label*='City']"
            if not await self.browser.wait_for_element(location_input, timeout=5):
                location_input = "input.jobs-search-box__text-input[aria-label*='location']"
            
            await self.browser.type_text(location_input, location, clear_first=True)
            await asyncio.sleep(1)
            
            # Search button
            search_button = "button.jobs-search-box__submit-button"
            if not await self.browser.wait_for_element(search_button, timeout=5):
                search_button = "button[aria-label='Search']"
            
            await self.browser.click_element(search_button)
            await asyncio.sleep(3)
            
        except Exception as e:
            logger.error(f"Failed to fill search form: {e}")
            raise
    
    async def _apply_search_filters(self, experience_levels: List[str] = None) -> None:
        """Apply search filters."""
        try:
            # Date posted filter (Past week)
            await self._apply_date_filter()
            
            # Experience level filter
            if experience_levels:
                await self._apply_experience_filter(experience_levels)
            
            # Job type filter (if needed)
            # await self._apply_job_type_filter()
            
        except Exception as e:
            logger.debug(f"Filter application failed: {e}")
    
    async def _apply_date_filter(self) -> None:
        """Apply date posted filter."""
        try:
            # Click date filter button
            date_filter_button = "button[aria-label*='Date posted']"
            if await self.browser.wait_for_element(date_filter_button, timeout=5):
                await self.browser.click_element(date_filter_button)
                await asyncio.sleep(1)
                
                # Select "Past week"
                past_week_option = "input[value='r604800']"
                if await self.browser.wait_for_element(past_week_option, timeout=3):
                    await self.browser.click_element(past_week_option)
                    await asyncio.sleep(1)
                
                # Apply filter
                apply_button = "button[data-control-name='filter_show_results']"
                if await self.browser.wait_for_element(apply_button, timeout=3):
                    await self.browser.click_element(apply_button)
                    await asyncio.sleep(2)
                    
        except Exception as e:
            logger.debug(f"Date filter application failed: {e}")
    
    async def _apply_experience_filter(self, experience_levels: List[str]) -> None:
        """Apply experience level filter."""
        try:
            # Click experience level filter
            experience_filter_button = "button[aria-label*='Experience level']"
            if await self.browser.wait_for_element(experience_filter_button, timeout=5):
                await self.browser.click_element(experience_filter_button)
                await asyncio.sleep(1)
                
                # Map experience levels to LinkedIn values
                level_mapping = {
                    "internship": "1",
                    "entry level": "2",
                    "associate": "3",
                    "mid-senior level": "4",
                    "director": "5",
                    "executive": "6"
                }
                
                for level in experience_levels:
                    level_value = level_mapping.get(level.lower())
                    if level_value:
                        level_checkbox = f"input[value='{level_value}']"
                        if await self.browser.wait_for_element(level_checkbox, timeout=2):
                            await self.browser.click_element(level_checkbox)
                            await asyncio.sleep(0.5)
                
                # Apply filter
                apply_button = "button[data-control-name='filter_show_results']"
                if await self.browser.wait_for_element(apply_button, timeout=3):
                    await self.browser.click_element(apply_button)
                    await asyncio.sleep(2)
                    
        except Exception as e:
            logger.debug(f"Experience filter application failed: {e}")
    
    async def _extract_job_listings(self) -> List[Dict[str, Any]]:
        """Extract job listings from search results."""
        jobs = []
        
        try:
            # Wait for job results to load
            job_cards_container = ".jobs-search__results-list"
            await self.browser.wait_for_element(job_cards_container, timeout=10)
            
            # Scroll to load more jobs
            await self.browser.human_like_scroll()
            await asyncio.sleep(2)
            
            # Get job card elements
            job_cards = "div.base-card"
            job_elements = await self.browser.page.query_selector_all(job_cards)
            
            logger.info(f"Found {len(job_elements)} job cards")
            
            for i, job_element in enumerate(job_elements[:15]):  # Limit to first 15 jobs
                try:
                    job_data = await self._extract_single_job(job_element)
                    if job_data:
                        jobs.append(job_data)
                        
                except Exception as e:
                    logger.debug(f"Failed to extract job {i}: {e}")
            
            return jobs
            
        except Exception as e:
            logger.error(f"Job extraction failed: {e}")
            return []
    
    async def _extract_single_job(self, job_element) -> Optional[Dict[str, Any]]:
        """Extract data from a single job card."""
        try:
            # Job title
            title_element = await job_element.query_selector("h3.base-search-card__title a")
            title = await title_element.text_content() if title_element else "Unknown"
            
            # Company name
            company_element = await job_element.query_selector("h4.base-search-card__subtitle a")
            if not company_element:
                company_element = await job_element.query_selector("h4.base-search-card__subtitle")
            company = await company_element.text_content() if company_element else "Unknown"
            
            # Location
            location_element = await job_element.query_selector("span.job-search-card__location")
            location = await location_element.text_content() if location_element else "Unknown"
            
            # Job URL
            link_element = await job_element.query_selector("h3.base-search-card__title a")
            job_url = await link_element.get_attribute("href") if link_element else ""
            
            # Posted date
            date_element = await job_element.query_selector("time")
            posted_date = await date_element.get_attribute("datetime") if date_element else ""
            
            # Easy Apply check
            easy_apply_element = await job_element.query_selector("span.job-search-card__easy-apply-label")
            is_easy_apply = easy_apply_element is not None
            
            job_data = {
                "title": title.strip(),
                "company": company.strip(),
                "location": location.strip(),
                "url": job_url,
                "posted_date": posted_date,
                "is_easy_apply": is_easy_apply,
                "source": "LinkedIn",
                "extracted_at": asyncio.get_event_loop().time()
            }
            
            return job_data
            
        except Exception as e:
            logger.debug(f"Single job extraction failed: {e}")
            return None
    
    async def is_easy_apply(self) -> bool:
        """Check if current job has Easy Apply option."""
        try:
            easy_apply_button = "button[aria-label*='Easy Apply']"
            return await self.browser.wait_for_element(easy_apply_button, timeout=5)
        except Exception:
            return False
    
    async def apply_easy_apply(self, job: Dict[str, Any], user_data: Dict[str, Any]) -> bool:
        """Apply to job using Easy Apply."""
        try:
            # Click Easy Apply button
            easy_apply_button = "button[aria-label*='Easy Apply']"
            if not await self.browser.click_element(easy_apply_button):
                return False
            
            await asyncio.sleep(2)
            
            # Handle Easy Apply flow
            return await self._handle_easy_apply_flow(job, user_data)
            
        except Exception as e:
            logger.error(f"Easy Apply failed: {e}")
            return False
    
    async def _handle_easy_apply_flow(self, job: Dict[str, Any], user_data: Dict[str, Any]) -> bool:
        """Handle the Easy Apply application flow."""
        try:
            max_steps = 5
            current_step = 0
            
            while current_step < max_steps:
                # Check if we're done
                if await self._is_application_complete():
                    logger.info("Easy Apply application completed")
                    return True
                
                # Handle current step
                if not await self._handle_easy_apply_step(user_data):
                    logger.error("Failed to handle Easy Apply step")
                    return False
                
                # Try to go to next step
                if not await self._go_to_next_step():
                    logger.error("Failed to proceed to next step")
                    return False
                
                current_step += 1
                await asyncio.sleep(2)
            
            logger.warning("Easy Apply flow exceeded maximum steps")
            return False
            
        except Exception as e:
            logger.error(f"Easy Apply flow failed: {e}")
            return False
    
    async def _handle_easy_apply_step(self, user_data: Dict[str, Any]) -> bool:
        """Handle a single step in Easy Apply flow."""
        try:
            # Upload resume if requested
            resume_upload = "input[type='file']"
            if await self.browser.is_element_visible(resume_upload):
                resume_path = user_data.get("resume_path")
                if resume_path:
                    await self.browser.upload_file(resume_upload, resume_path)
                    await asyncio.sleep(2)
            
            # Fill text inputs
            text_inputs = await self.browser.page.query_selector_all("input[type='text'], textarea")
            for input_element in text_inputs:
                await self._fill_application_field(input_element, user_data)
            
            # Handle dropdowns
            selects = await self.browser.page.query_selector_all("select")
            for select_element in selects:
                await self._handle_application_dropdown(select_element, user_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Easy Apply step handling failed: {e}")
            return False
    
    async def _fill_application_field(self, input_element, user_data: Dict[str, Any]) -> None:
        """Fill a specific application field."""
        try:
            # Get field label or placeholder
            label = await input_element.get_attribute("aria-label") or ""
            placeholder = await input_element.get_attribute("placeholder") or ""
            field_name = (label + " " + placeholder).lower()
            
            # Determine what to fill based on field name
            value = ""
            if "phone" in field_name:
                value = user_data.get("phone", "")
            elif "email" in field_name:
                value = user_data.get("email", "")
            elif "first name" in field_name:
                value = user_data.get("name", "").split()[0] if user_data.get("name") else ""
            elif "last name" in field_name:
                name_parts = user_data.get("name", "").split()
                value = name_parts[-1] if len(name_parts) > 1 else ""
            elif "linkedin" in field_name:
                value = user_data.get("linkedin_url", "")
            elif "github" in field_name:
                value = user_data.get("github_url", "")
            elif "website" in field_name or "portfolio" in field_name:
                value = user_data.get("portfolio_url", "")
            
            if value:
                await input_element.fill(value)
                await asyncio.sleep(0.5)
                
        except Exception as e:
            logger.debug(f"Field filling failed: {e}")
    
    async def _handle_application_dropdown(self, select_element, user_data: Dict[str, Any]) -> None:
        """Handle dropdown selections in application."""
        try:
            # Get dropdown label
            label = await select_element.get_attribute("aria-label") or ""
            
            # Handle work authorization
            if "authorization" in label.lower() or "eligible" in label.lower():
                work_auth = user_data.get("work_authorization", "")
                if "citizen" in work_auth.lower():
                    await select_element.select_option(label="Yes")
                elif "require sponsorship" in work_auth.lower():
                    await select_element.select_option(label="No")
            
            # Handle graduation date
            elif "graduation" in label.lower():
                grad_date = user_data.get("education", {}).get("graduation_date", "")
                if grad_date:
                    await select_element.select_option(value=grad_date)
            
        except Exception as e:
            logger.debug(f"Dropdown handling failed: {e}")
    
    async def _go_to_next_step(self) -> bool:
        """Go to next step in Easy Apply flow."""
        try:
            # Look for Next, Continue, or Submit buttons
            next_buttons = [
                "button[aria-label*='Continue']",
                "button[aria-label*='Next']", 
                "button[aria-label*='Submit']",
                "button[data-control-name='continue_unify']"
            ]
            
            for button_selector in next_buttons:
                if await self.browser.wait_for_element(button_selector, timeout=3):
                    await self.browser.click_element(button_selector)
                    await asyncio.sleep(2)
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to go to next step: {e}")
            return False
    
    async def _is_application_complete(self) -> bool:
        """Check if application is complete."""
        try:
            # Look for success indicators
            success_indicators = [
                "h3[data-test-modal-id='application-submitted-modal']",
                ".artdeco-inline-feedback--success",
                "text=Application submitted"
            ]
            
            for indicator in success_indicators:
                if await self.browser.is_element_visible(indicator):
                    return True
            
            return False
            
        except Exception as e:
            logger.debug(f"Application completion check failed: {e}")
            return False
    
    async def apply_external(self, job: Dict[str, Any], user_data: Dict[str, Any]) -> bool:
        """Handle external job application."""
        try:
            # Click Apply button (external)
            apply_button = "a[data-control-name='jobdetails_topcard_inapply']"
            if not await self.browser.click_element(apply_button):
                return False
            
            await asyncio.sleep(3)
            
            # This would redirect to external site
            # For now, we'll just log it as requiring manual action
            logger.info(f"External application required for {job.get('title')} at {job.get('company')}")
            return False  # Mark as requiring manual action
            
        except Exception as e:
            logger.error(f"External application failed: {e}")
            return False
