"""
Browser automation manager using browser-use library.
"""

import asyncio
import time
import random
from typing import Optional, Dict, Any, List
from pathlib import Path
from loguru import logger

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    logger.warning("playwright library not installed. Install with: pip install playwright")
    async_playwright = None
    Browser = None
    Page = None
    PLAYWRIGHT_AVAILABLE = False

try:
    from browser_use import <PERSON><PERSON><PERSON> as BrowserUse, BrowserConfig
    BROWSER_USE_AVAILABLE = True
except ImportError:
    logger.info("browser-use library not available (requires Python 3.11+)")
    BrowserUse = None
    BrowserConfig = None
    BROWSER_USE_AVAILABLE = False

from config.settings import BROWSER_CONFIG
from utils.logger import log_browser_action


class BrowserManager:
    """Manages browser automation using Playwright or browser-use library."""

    def __init__(self, headless: bool = False):
        """Initialize browser manager."""
        self.playwright = None
        self.browser = None
        self.page = None
        self.headless = headless
        self.config = BROWSER_CONFIG.copy()
        self.config["headless"] = headless
        self.use_playwright = PLAYWRIGHT_AVAILABLE and not BROWSER_USE_AVAILABLE

    async def start_browser(self) -> None:
        """Start browser instance."""
        if self.use_playwright:
            await self._start_playwright_browser()
        else:
            await self._start_browser_use()

    async def _start_playwright_browser(self) -> None:
        """Start browser using Playwright."""
        if not PLAYWRIGHT_AVAILABLE:
            raise ImportError("Playwright library not available")

        try:
            # Start Playwright
            self.playwright = await async_playwright().start()

            # Launch browser
            self.browser = await self.playwright.chromium.launch(
                headless=self.config["headless"],
                args=[
                    f'--window-size={self.config["window_size"][0]},{self.config["window_size"][1]}',
                    '--no-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )

            # Create context with user agent
            context = await self.browser.new_context(
                user_agent=self.config["user_agent"],
                viewport={"width": self.config["window_size"][0], "height": self.config["window_size"][1]}
            )

            # Create page
            self.page = await context.new_page()

            # Set timeouts
            self.page.set_default_timeout(self.config["timeout"] * 1000)

            logger.info("Playwright browser started successfully")
            log_browser_action("START_BROWSER", success=True)

        except Exception as e:
            logger.error(f"Failed to start Playwright browser: {e}")
            log_browser_action("START_BROWSER", success=False)
            raise

    async def _start_browser_use(self) -> None:
        """Start browser using browser-use library."""
        if not BROWSER_USE_AVAILABLE:
            raise ImportError("browser-use library not available")

        try:
            # Configure browser
            browser_config = BrowserConfig(
                headless=self.config["headless"],
                window_size=self.config["window_size"],
                user_agent=self.config["user_agent"]
            )

            # Start browser
            self.browser = BrowserUse(config=browser_config)
            await self.browser.start()

            # Get page
            self.page = await self.browser.new_page()

            # Set timeouts
            await self.page.set_default_timeout(self.config["timeout"] * 1000)

            logger.info("Browser-use browser started successfully")
            log_browser_action("START_BROWSER", success=True)

        except Exception as e:
            logger.error(f"Failed to start browser-use browser: {e}")
            log_browser_action("START_BROWSER", success=False)
            raise
    
    async def close_browser(self) -> None:
        """Close browser instance."""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

            logger.info("Browser closed successfully")
            log_browser_action("CLOSE_BROWSER", success=True)

        except Exception as e:
            logger.error(f"Failed to close browser: {e}")
            log_browser_action("CLOSE_BROWSER", success=False)
    
    async def navigate_to(self, url: str) -> bool:
        """Navigate to URL."""
        try:
            await self.page.goto(url)
            await self.random_delay(1, 3)
            
            logger.debug(f"Navigated to: {url}")
            log_browser_action("NAVIGATE", url, success=True)
            return True
            
        except Exception as e:
            logger.error(f"Failed to navigate to {url}: {e}")
            log_browser_action("NAVIGATE", url, success=False)
            return False
    
    async def wait_for_element(self, selector: str, timeout: int = 10) -> bool:
        """Wait for element to be visible."""
        try:
            await self.page.wait_for_selector(selector, timeout=timeout * 1000)
            log_browser_action("WAIT_FOR_ELEMENT", selector, success=True)
            return True
            
        except Exception as e:
            logger.debug(f"Element not found: {selector} - {e}")
            log_browser_action("WAIT_FOR_ELEMENT", selector, success=False)
            return False
    
    async def click_element(self, selector: str) -> bool:
        """Click element by selector."""
        try:
            await self.page.click(selector)
            await self.random_delay(0.5, 2)
            
            log_browser_action("CLICK", selector, success=True)
            return True
            
        except Exception as e:
            logger.error(f"Failed to click {selector}: {e}")
            log_browser_action("CLICK", selector, success=False)
            return False
    
    async def type_text(self, selector: str, text: str, clear_first: bool = True) -> bool:
        """Type text into element."""
        try:
            if clear_first:
                await self.page.fill(selector, "")
            
            await self.page.type(selector, text, delay=random.randint(50, 150))
            await self.random_delay(0.5, 1.5)
            
            log_browser_action("TYPE", f"{selector}: {text[:20]}...", success=True)
            return True
            
        except Exception as e:
            logger.error(f"Failed to type in {selector}: {e}")
            log_browser_action("TYPE", selector, success=False)
            return False
    
    async def select_option(self, selector: str, value: str) -> bool:
        """Select option from dropdown."""
        try:
            await self.page.select_option(selector, value)
            await self.random_delay(0.5, 1.5)
            
            log_browser_action("SELECT", f"{selector}: {value}", success=True)
            return True
            
        except Exception as e:
            logger.error(f"Failed to select option {value} in {selector}: {e}")
            log_browser_action("SELECT", selector, success=False)
            return False
    
    async def upload_file(self, selector: str, file_path: str) -> bool:
        """Upload file to input element."""
        try:
            if not Path(file_path).exists():
                logger.error(f"File not found: {file_path}")
                return False
            
            await self.page.set_input_files(selector, file_path)
            await self.random_delay(1, 3)
            
            log_browser_action("UPLOAD", f"{selector}: {Path(file_path).name}", success=True)
            return True
            
        except Exception as e:
            logger.error(f"Failed to upload file {file_path}: {e}")
            log_browser_action("UPLOAD", selector, success=False)
            return False
    
    async def get_text(self, selector: str) -> Optional[str]:
        """Get text content of element."""
        try:
            element = await self.page.query_selector(selector)
            if element:
                text = await element.text_content()
                log_browser_action("GET_TEXT", selector, success=True)
                return text.strip() if text else None
            return None
            
        except Exception as e:
            logger.error(f"Failed to get text from {selector}: {e}")
            log_browser_action("GET_TEXT", selector, success=False)
            return None
    
    async def get_attribute(self, selector: str, attribute: str) -> Optional[str]:
        """Get attribute value of element."""
        try:
            element = await self.page.query_selector(selector)
            if element:
                value = await element.get_attribute(attribute)
                log_browser_action("GET_ATTRIBUTE", f"{selector}[{attribute}]", success=True)
                return value
            return None
            
        except Exception as e:
            logger.error(f"Failed to get attribute {attribute} from {selector}: {e}")
            log_browser_action("GET_ATTRIBUTE", selector, success=False)
            return None
    
    async def is_element_visible(self, selector: str) -> bool:
        """Check if element is visible."""
        try:
            element = await self.page.query_selector(selector)
            if element:
                return await element.is_visible()
            return False
            
        except Exception as e:
            logger.debug(f"Element visibility check failed for {selector}: {e}")
            return False
    
    async def scroll_to_element(self, selector: str) -> bool:
        """Scroll to element."""
        try:
            await self.page.locator(selector).scroll_into_view_if_needed()
            await self.random_delay(0.5, 1)
            
            log_browser_action("SCROLL", selector, success=True)
            return True
            
        except Exception as e:
            logger.error(f"Failed to scroll to {selector}: {e}")
            log_browser_action("SCROLL", selector, success=False)
            return False
    
    async def take_screenshot(self, filename: str = None) -> str:
        """Take screenshot of current page."""
        try:
            if not filename:
                timestamp = int(time.time())
                filename = f"screenshot_{timestamp}.png"
            
            screenshot_path = Path("data") / "screenshots" / filename
            screenshot_path.parent.mkdir(parents=True, exist_ok=True)
            
            await self.page.screenshot(path=str(screenshot_path))
            
            logger.info(f"Screenshot saved: {screenshot_path}")
            return str(screenshot_path)
            
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
            return ""
    
    async def random_delay(self, min_seconds: float = 1, max_seconds: float = 3) -> None:
        """Add random delay to mimic human behavior."""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def human_like_scroll(self) -> None:
        """Perform human-like scrolling."""
        try:
            # Random scroll down
            scroll_amount = random.randint(200, 800)
            await self.page.evaluate(f"window.scrollBy(0, {scroll_amount})")
            await self.random_delay(1, 2)
            
            # Sometimes scroll back up a bit
            if random.random() < 0.3:
                scroll_back = random.randint(50, 200)
                await self.page.evaluate(f"window.scrollBy(0, -{scroll_back})")
                await self.random_delay(0.5, 1)
                
        except Exception as e:
            logger.debug(f"Human-like scroll failed: {e}")
    
    async def check_for_captcha(self) -> bool:
        """Check if CAPTCHA is present on page."""
        captcha_selectors = [
            "[data-testid='captcha']",
            ".captcha",
            "#captcha",
            "iframe[src*='recaptcha']",
            "iframe[src*='hcaptcha']",
            ".g-recaptcha",
            ".h-captcha"
        ]
        
        for selector in captcha_selectors:
            if await self.is_element_visible(selector):
                logger.warning("CAPTCHA detected on page")
                return True
        
        return False
    
    async def handle_popup(self) -> bool:
        """Handle common popups and modals."""
        popup_selectors = [
            "[data-testid='modal-close']",
            ".modal-close",
            ".close-button",
            "[aria-label='Close']",
            ".dismiss-button"
        ]
        
        for selector in popup_selectors:
            if await self.is_element_visible(selector):
                await self.click_element(selector)
                logger.info("Popup closed")
                return True
        
        return False
