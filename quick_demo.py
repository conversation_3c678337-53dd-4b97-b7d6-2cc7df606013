#!/usr/bin/env python3
"""
Quick demo script to showcase the job application automation system.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.config_manager import ConfigManager
from automation.browser_manager import BrowserManager
from notion_integration.notion_client import NotionClient
from utils.logger import setup_logging
from utils.ai_responder import AIResponder


async def demo_browser_automation():
    """Demonstrate browser automation capabilities."""
    print("🌐 Testing Browser Automation")
    print("-" * 40)
    
    try:
        browser_manager = BrowserManager(headless=True)
        await browser_manager.start_browser()
        print("✅ Browser started successfully")
        
        # Navigate to a job search page
        await browser_manager.navigate_to("https://www.linkedin.com/jobs")
        print("✅ Navigated to LinkedIn Jobs")
        
        # Take a screenshot
        screenshot_path = await browser_manager.take_screenshot("demo_linkedin.png")
        print(f"✅ Screenshot saved: {screenshot_path}")
        
        await browser_manager.close_browser()
        print("✅ Browser closed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Browser automation failed: {e}")
        return False


def demo_ai_responder():
    """Demonstrate AI response generation."""
    print("\n🤖 Testing AI Response Generation")
    print("-" * 40)
    
    try:
        ai_responder = AIResponder()
        
        if ai_responder.is_available():
            print("✅ AI responder is available")
            print(f"✅ Provider: {ai_responder.ai_config.get('provider', 'unknown')}")
            
            # Test question classification
            from utils.ai_responder import QuestionClassifier
            
            test_questions = [
                "Why are you interested in this position?",
                "What are your technical skills?",
                "Describe a challenging project you worked on.",
                "Where do you see yourself in 5 years?"
            ]
            
            print("\n📝 Question Classification Test:")
            for question in test_questions:
                category = QuestionClassifier.classify_question(question)
                can_handle = QuestionClassifier.can_ai_handle(question)
                print(f"  • '{question[:40]}...' → {category} ({'✅' if can_handle else '❌'})")
            
            return True
        else:
            print("⚠️  AI responder not available (API key may be missing)")
            return False
            
    except Exception as e:
        print(f"❌ AI responder test failed: {e}")
        return False


def demo_configuration():
    """Demonstrate configuration management."""
    print("\n⚙️  Testing Configuration Management")
    print("-" * 40)
    
    try:
        config_manager = ConfigManager()
        
        # Test default configuration
        job_prefs = config_manager.get_job_preferences()
        print(f"✅ Default job keywords: {len(job_prefs['keywords'])} configured")
        print(f"✅ Default locations: {len(job_prefs['locations'])} configured")
        
        # Test application settings
        app_settings = config_manager.get_application_settings()
        print(f"✅ Max daily applications: {app_settings['max_daily_applications']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def demo_encryption():
    """Demonstrate data encryption."""
    print("\n🔐 Testing Data Encryption")
    print("-" * 40)
    
    try:
        from config.encryption import EncryptionManager
        
        # Test data
        test_data = {
            "name": "Demo User",
            "email": "<EMAIL>",
            "secret_info": "This is encrypted data"
        }
        
        encryption_manager = EncryptionManager()
        password = "demo_password_123"
        
        # Encrypt
        encrypted_data = encryption_manager.encrypt_data(test_data, password)
        print(f"✅ Data encrypted: {len(encrypted_data)} bytes")
        
        # Decrypt
        decrypted_data = encryption_manager.decrypt_data(encrypted_data, password)
        print(f"✅ Data decrypted: {len(decrypted_data)} fields")
        
        # Verify integrity
        if decrypted_data == test_data:
            print("✅ Data integrity verified")
            return True
        else:
            print("❌ Data integrity check failed")
            return False
            
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        return False


def demo_job_search_simulation():
    """Simulate job search functionality."""
    print("\n🔍 Simulating Job Search")
    print("-" * 40)
    
    try:
        # Simulate finding jobs
        sample_jobs = [
            {
                "title": "Software Engineer Intern",
                "company": "Tech Corp",
                "location": "San Francisco, CA",
                "url": "https://example.com/job1",
                "source": "LinkedIn",
                "salary": "$25-35/hour",
                "posted_date": datetime.now().isoformat()
            },
            {
                "title": "Cloud Engineer Intern",
                "company": "Cloud Solutions Inc",
                "location": "Seattle, WA", 
                "url": "https://example.com/job2",
                "source": "Indeed",
                "salary": "$30-40/hour",
                "posted_date": datetime.now().isoformat()
            },
            {
                "title": "Full Stack Developer Intern",
                "company": "Startup XYZ",
                "location": "Remote",
                "url": "https://example.com/job3",
                "source": "LinkedIn",
                "salary": "$28-38/hour",
                "posted_date": datetime.now().isoformat()
            }
        ]
        
        print(f"✅ Found {len(sample_jobs)} sample jobs:")
        for job in sample_jobs:
            print(f"  • {job['title']} at {job['company']} ({job['location']})")
        
        # Simulate duplicate detection
        from core.duplicate_detector import DuplicateDetector
        
        duplicate_detector = DuplicateDetector()
        unique_jobs = duplicate_detector.remove_duplicate_jobs(sample_jobs + sample_jobs[:1])  # Add one duplicate
        print(f"✅ Duplicate detection: {len(sample_jobs)} → {len(unique_jobs)} unique jobs")
        
        return True
        
    except Exception as e:
        print(f"❌ Job search simulation failed: {e}")
        return False


def demo_notion_integration():
    """Demonstrate Notion integration (without API key)."""
    print("\n📊 Testing Notion Integration")
    print("-" * 40)
    
    try:
        notion_client = NotionClient()
        
        # Test connection (will fail without API key, but shouldn't crash)
        connection_ok = notion_client.test_connection()
        
        if connection_ok:
            print("✅ Notion connection successful")
        else:
            print("⚠️  Notion connection failed (API key needed)")
            print("   This is expected for the demo")
        
        print("✅ Notion client initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Notion integration test failed: {e}")
        return False


async def run_comprehensive_demo():
    """Run comprehensive system demonstration."""
    print("🚀 Job Application Automation System - Live Demo")
    print("=" * 60)
    
    # Setup logging
    setup_logging(debug=False)
    
    # Run all demo components
    demos = [
        ("Configuration Management", demo_configuration),
        ("Data Encryption", demo_encryption),
        ("AI Response Generation", demo_ai_responder),
        ("Job Search Simulation", demo_job_search_simulation),
        ("Notion Integration", demo_notion_integration),
        ("Browser Automation", demo_browser_automation)
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        try:
            if asyncio.iscoroutinefunction(demo_func):
                result = await demo_func()
            else:
                result = demo_func()
            results.append((demo_name, result))
        except Exception as e:
            print(f"❌ {demo_name} failed with exception: {e}")
            results.append((demo_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Demo Results Summary")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for demo_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {demo_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} components working")
    
    if passed >= total - 1:  # Allow 1 failure (likely Notion without API key)
        print("\n🎉 System is working excellently!")
        print("\n🚀 Ready for Production Use!")
        print("\nWhat this system can do:")
        print("• Automatically search for jobs on LinkedIn, Indeed, etc.")
        print("• Fill out application forms with your data")
        print("• Use AI to answer novel questions")
        print("• Track all applications in Notion")
        print("• Run daily automation on schedule")
        print("• Handle errors gracefully with recovery")
        
        print("\n📋 Next Steps:")
        print("1. Get Notion API key: https://www.notion.so/my-integrations")
        print("2. Update .env file with your Notion API key")
        print("3. Run: python3 main.py setup")
        print("4. Start applying: python3 main.py apply --dry-run")
        
    else:
        print(f"\n⚠️  {total - passed} components need attention")
        print("Check the output above for details")
    
    return passed >= total - 1


if __name__ == "__main__":
    try:
        success = asyncio.run(run_comprehensive_demo())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Demo cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        sys.exit(1)
