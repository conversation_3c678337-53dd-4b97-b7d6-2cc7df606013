# Job Application Automation System

An intelligent job application agent that automates the job search and application process using Python 3.12 and the browser-use library.

## Features

- 🤖 **Automated Job Applications**: Apply to jobs on LinkedIn, Indeed, and other platforms
- 🔐 **Secure Data Storage**: Encrypted storage of personal information and credentials
- 📊 **Notion Integration**: Track applications, interviews, and responses in Notion
- 🧠 **AI-Powered Responses**: Generate responses to novel application questions
- 🔍 **Smart Duplicate Detection**: Avoid applying to the same job multiple times
- ⏰ **Scheduled Automation**: Daily automated job searches and applications
- 🛡️ **Privacy-Focused**: Minimal data retention with strong encryption

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/HemanthKiranPolu/JOB_Auto_Appllier.git
cd JOB_Auto_Appllier

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys
nano .env
```

### 3. Initial Setup

```bash
# Run the setup process
python main.py setup
```

This will guide you through:
- Personal information collection
- Resume upload
- Job preferences configuration
- Notion API setup
- AI integration setup

### 4. Run Job Applications

```bash
# Apply to jobs immediately
python main.py apply

# Dry run (test without applying)
python main.py apply --dry-run

# Schedule daily automation
python main.py schedule --time 08:00
```

## Commands

### Setup
```bash
python main.py setup
```
Run the initial setup to configure your profile and preferences.

### Apply to Jobs
```bash
python main.py apply [OPTIONS]

Options:
  --dry-run              Run without actually applying to jobs
  --max-applications N   Maximum number of applications per run (default: 10)
```

### Schedule Daily Automation
```bash
python main.py schedule [OPTIONS]

Options:
  --time HH:MM          Daily run time in 24-hour format (default: 08:00)
```

### Check Status
```bash
python main.py status
```
Check system status and configuration.

### View Configuration
```bash
python main.py config
```
Display current configuration (without sensitive data).

## Configuration

### Required API Keys

1. **Notion API Key**: Create at https://www.notion.so/my-integrations
2. **AI API Key**: Either OpenAI or Anthropic for generating responses

### Job Search Preferences

During setup, you'll configure:
- Target job titles and keywords
- Preferred locations
- Experience levels
- Salary expectations
- Work authorization status

### Application Data

The system securely stores:
- Resume (PDF format)
- Personal information (name, email, phone, address)
- Professional profiles (LinkedIn, GitHub, portfolio)
- Education details
- Standard application responses

## How It Works

### Daily Workflow

1. **8:00 AM**: Agent starts automatically (configurable)
2. **Job Search**: Searches LinkedIn, Indeed for new postings
3. **Filtering**: Removes duplicates and applies preferences
4. **Applications**: Automatically fills forms and submits applications
5. **Tracking**: Logs all activities to Notion database
6. **Reporting**: Updates status and handles errors

### Supported Platforms

- ✅ **LinkedIn**: Easy Apply and external applications
- ✅ **Indeed**: Direct applications and redirects
- 🚧 **Glassdoor**: Coming soon
- 🚧 **Company Websites**: Coming soon

### Smart Features

- **Duplicate Detection**: Prevents applying to same job twice
- **Company Limits**: Configurable max applications per company
- **CAPTCHA Detection**: Alerts when manual intervention needed
- **Error Recovery**: Robust handling of automation failures
- **Human-like Behavior**: Random delays and scrolling patterns

## Notion Integration

### Database Schema

The system creates a Notion database with:
- Job Title, Company, Location
- Application Status, Source, Date
- Salary Range, Job URL
- Description, Notes, Follow-up Date

### Status Tracking

- **Applied**: Successfully submitted
- **Interview Scheduled**: Manual update
- **Rejected**: Manual update
- **Offer Received**: Manual update
- **Manual Action Needed**: Requires intervention
- **Skipped**: Filtered out

## Security & Privacy

### Data Encryption
- All personal data encrypted with Fernet (AES 128)
- Master password required for access
- Local storage only - no cloud uploads

### Privacy Features
- Minimal data collection
- No tracking or analytics
- Secure credential storage
- Optional data retention limits

## Troubleshooting

### Common Issues

1. **Browser automation fails**
   - Check if browser-use library is installed
   - Verify Chrome/Chromium is available
   - Try running with `--headless=false` for debugging

2. **LinkedIn login issues**
   - Verify credentials in setup
   - Check for 2FA requirements
   - LinkedIn may require manual verification

3. **Notion integration fails**
   - Verify API key is correct
   - Check database permissions
   - Ensure integration has access to workspace

4. **CAPTCHA blocking**
   - Normal behavior for bot detection
   - System will log and skip these jobs
   - Manual application may be required

### Debug Mode

```bash
python main.py apply --debug
```

### Logs

Check logs in the `logs/` directory:
- `job_agent.log`: General application logs
- `applications.log`: Application-specific events
- `errors.log`: Error messages only

## Development

### Project Structure

```
├── main.py                 # CLI entry point
├── config/                 # Configuration management
├── setup/                  # Initial setup process
├── automation/             # Browser automation
├── notion_integration/     # Notion API client
├── core/                   # Application logic
├── utils/                  # Utilities and helpers
└── data/                   # Encrypted data storage
```

### Adding New Job Portals

1. Create automation module in `automation/`
2. Implement job search and application logic
3. Add to `JobSearcher` in `core/job_searcher.py`
4. Update configuration in `config/settings.py`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This tool is for educational and personal use only. Users are responsible for:
- Complying with job platform terms of service
- Ensuring application accuracy and authenticity
- Respecting rate limits and automation policies
- Following applicable laws and regulations

Use responsibly and ethically. The authors are not responsible for any misuse or consequences of using this software.
