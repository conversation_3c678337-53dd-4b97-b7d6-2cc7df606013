#!/usr/bin/env python3
"""
Setup script for Job Application Automation System.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="job-application-agent",
    version="1.0.0",
    description="Intelligent job application automation system using browser-use library",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Hemanth Kiran Polu",
    author_email="<EMAIL>",
    url="https://github.com/HemanthKiranPolu/JOB_Auto_Appllier",
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    python_requires=">=3.12",
    entry_points={
        "console_scripts": [
            "job-agent=main:cli",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.12",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Office/Business",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="job application automation browser selenium linkedin indeed",
    project_urls={
        "Bug Reports": "https://github.com/HemanthKiranPolu/JOB_Auto_Appllier/issues",
        "Source": "https://github.com/HemanthKiranPolu/JOB_Auto_Appllier",
        "Documentation": "https://github.com/HemanthKiranPolu/JOB_Auto_Appllier/blob/main/README.md",
    },
)
