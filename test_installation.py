#!/usr/bin/env python3
"""
Test script to verify the job application automation system installation.
"""

import sys
import importlib
from pathlib import Path

def test_python_version():
    """Test Python version compatibility."""
    print("Testing Python version...")
    version = sys.version_info

    if version.major == 3 and version.minor >= 12:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Fully Compatible")
        return True
    elif version.major == 3 and version.minor >= 9:
        print(f"⚠️  Python {version.major}.{version.minor}.{version.micro} - Compatible (some features may be limited)")
        print("   Note: browser-use library requires Python 3.11+, using <PERSON><PERSON> instead")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.9+")
        return False

def test_required_packages():
    """Test if required packages are installed."""
    print("\nTesting required packages...")
    
    required_packages = [
        "click",
        "loguru", 
        "cryptography",
        "schedule",
        "pydantic",
        "aiofiles",
        "requests",
        "beautifulsoup4",
        "pandas",
        "python-dateutil"
    ]
    
    optional_packages = [
        "browser_use",
        "notion_client",
        "openai",
        "anthropic",
        "selenium"
    ]
    
    all_good = True
    
    # Test required packages
    for package in required_packages:
        try:
            # Handle special package name mappings
            module_name = package.replace("-", "_")
            if package == "beautifulsoup4":
                module_name = "bs4"
            elif package == "python-dateutil":
                module_name = "dateutil"

            importlib.import_module(module_name)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Not installed")
            all_good = False
    
    # Test optional packages
    print("\nOptional packages:")
    for package in optional_packages:
        try:
            importlib.import_module(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"⚠️  {package} - Not installed (optional)")
    
    return all_good

def test_project_structure():
    """Test if project structure is correct."""
    print("\nTesting project structure...")
    
    required_dirs = [
        "config",
        "setup", 
        "automation",
        "notion_integration",
        "core",
        "utils"
    ]
    
    required_files = [
        "main.py",
        "requirements.txt",
        "README.md",
        ".env.example"
    ]
    
    all_good = True
    
    # Test directories
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists() and dir_path.is_dir():
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ - Missing")
            all_good = False
    
    # Test files
    for file_name in required_files:
        file_path = Path(file_name)
        if file_path.exists() and file_path.is_file():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} - Missing")
            all_good = False
    
    return all_good

def test_module_imports():
    """Test if project modules can be imported."""
    print("\nTesting module imports...")
    
    modules_to_test = [
        "config.config_manager",
        "config.encryption",
        "setup.initial_setup",
        "automation.browser_manager",
        "notion_integration.notion_client",
        "core.application_processor",
        "utils.logger",
        "utils.scheduler"
    ]
    
    all_good = True
    
    for module in modules_to_test:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - Import failed: {e}")
            all_good = False
        except Exception as e:
            print(f"⚠️  {module} - Import warning: {e}")
    
    return all_good

def test_data_directories():
    """Test if data directories can be created."""
    print("\nTesting data directory creation...")
    
    try:
        # Create data and logs directories
        data_dir = Path("data")
        logs_dir = Path("logs")
        
        data_dir.mkdir(exist_ok=True)
        logs_dir.mkdir(exist_ok=True)
        
        if data_dir.exists() and logs_dir.exists():
            print("✅ Data directories created successfully")
            return True
        else:
            print("❌ Failed to create data directories")
            return False
            
    except Exception as e:
        print(f"❌ Data directory creation failed: {e}")
        return False

def test_cli_interface():
    """Test if CLI interface works."""
    print("\nTesting CLI interface...")
    
    try:
        # Try importing the main CLI
        from main import cli
        print("✅ CLI interface imported successfully")
        return True
    except Exception as e:
        print(f"❌ CLI interface test failed: {e}")
        return False

def run_all_tests():
    """Run all installation tests."""
    print("🧪 Job Application Automation System - Installation Test")
    print("=" * 60)
    
    tests = [
        ("Python Version", test_python_version),
        ("Required Packages", test_required_packages),
        ("Project Structure", test_project_structure),
        ("Module Imports", test_module_imports),
        ("Data Directories", test_data_directories),
        ("CLI Interface", test_cli_interface)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} - Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Installation looks good.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and configure your API keys")
        print("2. Run: python main.py setup")
        print("3. Run: python main.py apply --dry-run")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the installation.")
        print("\nTo fix issues:")
        print("1. Install missing packages: pip install -r requirements.txt")
        print("2. Check Python version (requires 3.12+)")
        print("3. Verify all project files are present")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
