"""
Comprehensive error handling and recovery mechanisms.
"""

import asyncio
import traceback
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timedelta
from loguru import logger

from utils.logger import log_error_with_context


class E<PERSON><PERSON><PERSON><PERSON><PERSON>:
    """Handles errors and implements recovery strategies."""
    
    def __init__(self):
        """Initialize error handler."""
        self.error_counts = {}
        self.last_errors = {}
        self.recovery_strategies = {}
        self._setup_recovery_strategies()
    
    def _setup_recovery_strategies(self) -> None:
        """Setup recovery strategies for different error types."""
        self.recovery_strategies = {
            "browser_timeout": self._recover_browser_timeout,
            "element_not_found": self._recover_element_not_found,
            "network_error": self._recover_network_error,
            "captcha_detected": self._recover_captcha,
            "login_failed": self._recover_login_failed,
            "application_failed": self._recover_application_failed,
            "notion_api_error": self._recover_notion_error,
            "ai_api_error": self._recover_ai_error
        }
    
    async def handle_error(
        self, 
        error: Exception, 
        context: Dict[str, Any],
        error_type: str = None,
        retry_func: Callable = None,
        max_retries: int = 3
    ) -> bool:
        """Handle error with recovery strategy."""
        try:
            # Classify error if not provided
            if not error_type:
                error_type = self._classify_error(error)
            
            # Log error with context
            log_error_with_context(error, context)
            
            # Track error frequency
            self._track_error(error_type, context)
            
            # Check if we should attempt recovery
            if not self._should_attempt_recovery(error_type):
                logger.warning(f"Too many {error_type} errors, skipping recovery")
                return False
            
            # Attempt recovery
            recovery_success = await self._attempt_recovery(error_type, context)
            
            if recovery_success and retry_func:
                # Retry the original operation
                for attempt in range(max_retries):
                    try:
                        await retry_func()
                        logger.info(f"Recovery successful after {attempt + 1} attempts")
                        return True
                    except Exception as retry_error:
                        logger.warning(f"Retry attempt {attempt + 1} failed: {retry_error}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(2 ** attempt)  # Exponential backoff
            
            return recovery_success
            
        except Exception as recovery_error:
            logger.error(f"Error recovery failed: {recovery_error}")
            return False
    
    def _classify_error(self, error: Exception) -> str:
        """Classify error type based on exception."""
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()
        
        if "timeout" in error_str or "timeout" in error_type:
            return "browser_timeout"
        elif "element" in error_str and ("not found" in error_str or "not visible" in error_str):
            return "element_not_found"
        elif "network" in error_str or "connection" in error_str:
            return "network_error"
        elif "captcha" in error_str:
            return "captcha_detected"
        elif "login" in error_str or "authentication" in error_str:
            return "login_failed"
        elif "notion" in error_str:
            return "notion_api_error"
        elif "openai" in error_str or "anthropic" in error_str:
            return "ai_api_error"
        else:
            return "unknown_error"
    
    def _track_error(self, error_type: str, context: Dict[str, Any]) -> None:
        """Track error frequency and context."""
        current_time = datetime.now()
        
        if error_type not in self.error_counts:
            self.error_counts[error_type] = []
        
        # Add current error
        self.error_counts[error_type].append({
            "timestamp": current_time,
            "context": context
        })
        
        # Clean old errors (older than 1 hour)
        cutoff_time = current_time - timedelta(hours=1)
        self.error_counts[error_type] = [
            error for error in self.error_counts[error_type]
            if error["timestamp"] > cutoff_time
        ]
        
        # Store last error
        self.last_errors[error_type] = {
            "timestamp": current_time,
            "context": context
        }
    
    def _should_attempt_recovery(self, error_type: str) -> bool:
        """Check if recovery should be attempted."""
        if error_type not in self.error_counts:
            return True
        
        # Don't attempt recovery if too many recent errors
        recent_errors = len(self.error_counts[error_type])
        
        if error_type == "captcha_detected":
            return recent_errors < 2  # Only try once for CAPTCHA
        elif error_type in ["browser_timeout", "network_error"]:
            return recent_errors < 5  # Allow more retries for transient errors
        else:
            return recent_errors < 3  # Default limit
    
    async def _attempt_recovery(self, error_type: str, context: Dict[str, Any]) -> bool:
        """Attempt recovery based on error type."""
        try:
            recovery_func = self.recovery_strategies.get(error_type)
            if recovery_func:
                return await recovery_func(context)
            else:
                logger.warning(f"No recovery strategy for error type: {error_type}")
                return False
                
        except Exception as e:
            logger.error(f"Recovery attempt failed: {e}")
            return False
    
    async def _recover_browser_timeout(self, context: Dict[str, Any]) -> bool:
        """Recover from browser timeout."""
        try:
            logger.info("Attempting browser timeout recovery")
            
            # Wait a bit longer
            await asyncio.sleep(5)
            
            # Try to refresh page if browser manager available
            browser_manager = context.get("browser_manager")
            if browser_manager:
                await browser_manager.page.reload()
                await asyncio.sleep(3)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Browser timeout recovery failed: {e}")
            return False
    
    async def _recover_element_not_found(self, context: Dict[str, Any]) -> bool:
        """Recover from element not found."""
        try:
            logger.info("Attempting element not found recovery")
            
            browser_manager = context.get("browser_manager")
            if not browser_manager:
                return False
            
            # Try scrolling to make element visible
            await browser_manager.human_like_scroll()
            await asyncio.sleep(2)
            
            # Handle any popups that might be blocking
            await browser_manager.handle_popup()
            
            return True
            
        except Exception as e:
            logger.error(f"Element not found recovery failed: {e}")
            return False
    
    async def _recover_network_error(self, context: Dict[str, Any]) -> bool:
        """Recover from network error."""
        try:
            logger.info("Attempting network error recovery")
            
            # Wait for network to stabilize
            await asyncio.sleep(10)
            
            # Try to navigate to a simple page first
            browser_manager = context.get("browser_manager")
            if browser_manager:
                await browser_manager.navigate_to("https://www.google.com")
                await asyncio.sleep(3)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Network error recovery failed: {e}")
            return False
    
    async def _recover_captcha(self, context: Dict[str, Any]) -> bool:
        """Handle CAPTCHA detection."""
        try:
            logger.warning("CAPTCHA detected - manual intervention required")
            
            # Take screenshot for manual review
            browser_manager = context.get("browser_manager")
            if browser_manager:
                screenshot_path = await browser_manager.take_screenshot("captcha_detected.png")
                logger.info(f"CAPTCHA screenshot saved: {screenshot_path}")
            
            # Log job for manual application
            job_data = context.get("job_data", {})
            if job_data:
                logger.info(f"Job requires manual application: {job_data.get('title')} at {job_data.get('company')}")
            
            return False  # CAPTCHA requires manual intervention
            
        except Exception as e:
            logger.error(f"CAPTCHA recovery failed: {e}")
            return False
    
    async def _recover_login_failed(self, context: Dict[str, Any]) -> bool:
        """Recover from login failure."""
        try:
            logger.warning("Login failed - may require manual verification")
            
            # Take screenshot
            browser_manager = context.get("browser_manager")
            if browser_manager:
                await browser_manager.take_screenshot("login_failed.png")
            
            # For now, don't attempt automatic recovery
            # This might require 2FA or manual verification
            return False
            
        except Exception as e:
            logger.error(f"Login recovery failed: {e}")
            return False
    
    async def _recover_application_failed(self, context: Dict[str, Any]) -> bool:
        """Recover from application failure."""
        try:
            logger.info("Attempting application failure recovery")
            
            # Take screenshot for debugging
            browser_manager = context.get("browser_manager")
            if browser_manager:
                await browser_manager.take_screenshot("application_failed.png")
                
                # Try to go back and retry
                await browser_manager.page.go_back()
                await asyncio.sleep(3)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Application recovery failed: {e}")
            return False
    
    async def _recover_notion_error(self, context: Dict[str, Any]) -> bool:
        """Recover from Notion API error."""
        try:
            logger.info("Attempting Notion API recovery")
            
            # Wait for API rate limit to reset
            await asyncio.sleep(5)
            
            # Test connection
            notion_client = context.get("notion_client")
            if notion_client and notion_client.test_connection():
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Notion recovery failed: {e}")
            return False
    
    async def _recover_ai_error(self, context: Dict[str, Any]) -> bool:
        """Recover from AI API error."""
        try:
            logger.info("Attempting AI API recovery")
            
            # Wait for API rate limit
            await asyncio.sleep(10)
            
            return True
            
        except Exception as e:
            logger.error(f"AI recovery failed: {e}")
            return False
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors."""
        summary = {}
        
        for error_type, errors in self.error_counts.items():
            summary[error_type] = {
                "count": len(errors),
                "last_occurrence": self.last_errors.get(error_type, {}).get("timestamp"),
                "recent_contexts": [error["context"] for error in errors[-3:]]  # Last 3 contexts
            }
        
        return summary
    
    def clear_error_history(self) -> None:
        """Clear error tracking history."""
        self.error_counts.clear()
        self.last_errors.clear()
        logger.info("Error history cleared")


class CriticalErrorHandler:
    """Handles critical errors that require immediate attention."""
    
    CRITICAL_ERRORS = [
        "browser_crash",
        "data_corruption",
        "security_breach",
        "system_failure"
    ]
    
    @staticmethod
    async def handle_critical_error(error: Exception, context: Dict[str, Any]) -> None:
        """Handle critical system errors."""
        try:
            logger.critical(f"CRITICAL ERROR: {error}")
            logger.critical(f"Context: {context}")
            
            # Take emergency screenshot if possible
            browser_manager = context.get("browser_manager")
            if browser_manager:
                try:
                    await browser_manager.take_screenshot("critical_error.png")
                except:
                    pass
            
            # Save error details
            error_details = {
                "timestamp": datetime.now().isoformat(),
                "error": str(error),
                "traceback": traceback.format_exc(),
                "context": context
            }
            
            # Could implement notification system here
            # (email, Slack, etc.)
            
        except Exception as e:
            logger.error(f"Critical error handler failed: {e}")


# Decorator for automatic error handling
def handle_errors(error_type: str = None, max_retries: int = 3):
    """Decorator for automatic error handling."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            error_handler = ErrorHandler()
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        # Last attempt, handle error
                        context = {
                            "function": func.__name__,
                            "args": str(args)[:100],
                            "kwargs": str(kwargs)[:100],
                            "attempt": attempt + 1
                        }
                        
                        await error_handler.handle_error(e, context, error_type)
                        raise
                    else:
                        # Retry with exponential backoff
                        await asyncio.sleep(2 ** attempt)
            
        return wrapper
    return decorator
