"""
Daily automation scheduling system.
"""

import time
import schedule
import asyncio
from datetime import datetime, timedelta
from typing import Callable, Optional
from loguru import logger

from core.application_processor import ApplicationProcessor


class JobScheduler:
    """Handles scheduling of daily job application automation."""
    
    def __init__(self):
        """Initialize scheduler."""
        self.is_running = False
        self.processor = None
        
    def schedule_daily_run(self, run_time: str = "08:00") -> None:
        """Schedule daily job application run."""
        try:
            # Validate time format
            datetime.strptime(run_time, "%H:%M")
            
            # Schedule the job
            schedule.every().day.at(run_time).do(self._run_daily_automation)
            
            logger.info(f"Daily automation scheduled for {run_time}")
            
        except ValueError:
            logger.error(f"Invalid time format: {run_time}. Use HH:MM format.")
            raise
    
    def schedule_weekly_summary(self, day: str = "sunday", time: str = "18:00") -> None:
        """Schedule weekly summary report."""
        try:
            # Validate time format
            datetime.strptime(time, "%H:%M")
            
            # Schedule weekly summary
            getattr(schedule.every(), day.lower()).at(time).do(self._send_weekly_summary)
            
            logger.info(f"Weekly summary scheduled for {day} at {time}")
            
        except (ValueError, AttributeError):
            logger.error(f"Invalid day or time format: {day} {time}")
            raise
    
    def _run_daily_automation(self) -> None:
        """Run the daily automation process."""
        logger.info("Starting scheduled daily automation")
        
        try:
            # Create processor instance
            self.processor = ApplicationProcessor(
                dry_run=False,
                max_applications=50  # Default daily limit
            )
            
            # Run automation
            asyncio.run(self.processor.run())
            
            logger.info("Daily automation completed successfully")
            
        except Exception as e:
            logger.error(f"Daily automation failed: {e}")
            # Could send notification here
    
    def _send_weekly_summary(self) -> None:
        """Send weekly summary report."""
        logger.info("Generating weekly summary")
        
        try:
            # This would integrate with Notion to generate summary
            # For now, just log the event
            logger.info("Weekly summary generated (implementation pending)")
            
        except Exception as e:
            logger.error(f"Weekly summary generation failed: {e}")
    
    def run(self) -> None:
        """Start the scheduler."""
        self.is_running = True
        logger.info("Scheduler started")
        
        try:
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logger.info("Scheduler stopped by user")
            self.stop()
        except Exception as e:
            logger.error(f"Scheduler error: {e}")
            raise
    
    def stop(self) -> None:
        """Stop the scheduler."""
        self.is_running = False
        schedule.clear()
        logger.info("Scheduler stopped")
    
    def get_next_run_time(self) -> Optional[datetime]:
        """Get the next scheduled run time."""
        jobs = schedule.get_jobs()
        if not jobs:
            return None
        
        next_run = min(job.next_run for job in jobs)
        return next_run
    
    def list_scheduled_jobs(self) -> list:
        """List all scheduled jobs."""
        jobs = []
        for job in schedule.get_jobs():
            jobs.append({
                "job": str(job.job_func),
                "next_run": job.next_run,
                "interval": job.interval,
                "unit": job.unit
            })
        return jobs
    
    def run_now(self) -> None:
        """Run automation immediately (for testing)."""
        logger.info("Running automation immediately")
        self._run_daily_automation()


class SchedulerConfig:
    """Configuration for scheduler settings."""
    
    DEFAULT_DAILY_TIME = "08:00"
    DEFAULT_WEEKLY_DAY = "sunday"
    DEFAULT_WEEKLY_TIME = "18:00"
    
    @staticmethod
    def validate_time_format(time_str: str) -> bool:
        """Validate time format (HH:MM)."""
        try:
            datetime.strptime(time_str, "%H:%M")
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_day_format(day_str: str) -> bool:
        """Validate day format."""
        valid_days = [
            "monday", "tuesday", "wednesday", "thursday", 
            "friday", "saturday", "sunday"
        ]
        return day_str.lower() in valid_days
