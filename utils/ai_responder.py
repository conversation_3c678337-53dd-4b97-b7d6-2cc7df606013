"""
AI-powered response generation for novel application questions.
"""

import asyncio
from typing import Dict, Any, Optional, List
from loguru import logger

try:
    import openai
except ImportError:
    openai = None

try:
    import anthropic
except ImportError:
    anthropic = None

from config.config_manager import ConfigManager


class AIResponder:
    """Generates AI-powered responses to application questions."""
    
    def __init__(self):
        """Initialize AI responder."""
        self.config_manager = ConfigManager()
        self.ai_config = self.config_manager.get_ai_config()
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize AI client based on configuration."""
        try:
            provider = self.ai_config.get("provider", "none")
            
            if provider == "openai" and openai:
                openai.api_key = self.ai_config.get("api_key")
                self.client = openai
                logger.info("OpenAI client initialized")
                
            elif provider == "anthropic" and anthropic:
                self.client = anthropic.Anthropic(
                    api_key=self.ai_config.get("api_key")
                )
                logger.info("Anthropic client initialized")
                
            elif provider == "none":
                logger.info("AI responder disabled")
                
            else:
                logger.warning(f"AI provider '{provider}' not available or not configured")
                
        except Exception as e:
            logger.error(f"Failed to initialize AI client: {e}")
    
    async def generate_response(
        self, 
        question: str, 
        user_data: Dict[str, Any],
        job_context: Dict[str, Any] = None
    ) -> Optional[str]:
        """Generate response to application question."""
        if not self.client:
            logger.warning("AI client not available")
            return None
        
        try:
            # Build context from user data
            context = self._build_context(user_data, job_context)
            
            # Generate response based on provider
            provider = self.ai_config.get("provider")
            
            if provider == "openai":
                response = await self._generate_openai_response(question, context)
            elif provider == "anthropic":
                response = await self._generate_anthropic_response(question, context)
            else:
                return None
            
            logger.info(f"Generated AI response for question: {question[:50]}...")
            return response
            
        except Exception as e:
            logger.error(f"AI response generation failed: {e}")
            return None
    
    def _build_context(self, user_data: Dict[str, Any], job_context: Dict[str, Any] = None) -> str:
        """Build context string from user data."""
        context_parts = []
        
        # Personal information
        name = user_data.get("name", "")
        if name:
            context_parts.append(f"Name: {name}")
        
        # Education
        education = user_data.get("education", {})
        if education:
            degree = education.get("degree", "")
            university = education.get("university", "")
            grad_date = education.get("graduation_date", "")
            
            if degree and university:
                context_parts.append(f"Education: {degree} from {university}")
                if grad_date:
                    context_parts.append(f"Graduation: {grad_date}")
        
        # Skills
        skills = user_data.get("skills", [])
        if skills:
            context_parts.append(f"Technical Skills: {', '.join(skills)}")
        
        # Work authorization
        work_auth = user_data.get("work_authorization", "")
        if work_auth:
            context_parts.append(f"Work Authorization: {work_auth}")
        
        # Job context
        if job_context:
            company = job_context.get("company", "")
            title = job_context.get("title", "")
            if company and title:
                context_parts.append(f"Applying for: {title} at {company}")
        
        return "\n".join(context_parts)
    
    async def _generate_openai_response(self, question: str, context: str) -> Optional[str]:
        """Generate response using OpenAI."""
        try:
            prompt = self._create_prompt(question, context)
            
            response = await openai.ChatCompletion.acreate(
                model=self.ai_config.get("model", "gpt-4-turbo-preview"),
                messages=[
                    {
                        "role": "system",
                        "content": "You are helping someone answer job application questions. Provide professional, concise, and honest responses based on the provided context."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                max_tokens=self.ai_config.get("max_tokens", 500),
                temperature=self.ai_config.get("temperature", 0.7)
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI response generation failed: {e}")
            return None
    
    async def _generate_anthropic_response(self, question: str, context: str) -> Optional[str]:
        """Generate response using Anthropic Claude."""
        try:
            prompt = self._create_prompt(question, context)
            
            response = await self.client.messages.create(
                model=self.ai_config.get("model", "claude-3-sonnet-20240229"),
                max_tokens=self.ai_config.get("max_tokens", 500),
                temperature=self.ai_config.get("temperature", 0.7),
                messages=[
                    {
                        "role": "user",
                        "content": f"You are helping someone answer job application questions. Provide professional, concise, and honest responses based on the provided context.\n\n{prompt}"
                    }
                ]
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            logger.error(f"Anthropic response generation failed: {e}")
            return None
    
    def _create_prompt(self, question: str, context: str) -> str:
        """Create prompt for AI model."""
        return f"""
Context about the applicant:
{context}

Application Question: {question}

Please provide a professional response to this application question based on the context provided. The response should be:
- Honest and accurate
- Professional in tone
- Concise (1-3 sentences)
- Relevant to the question asked
- Appropriate for a job application

Response:"""
    
    def is_available(self) -> bool:
        """Check if AI responder is available."""
        return self.client is not None
    
    def get_supported_question_types(self) -> List[str]:
        """Get list of question types that can be handled."""
        return [
            "Why are you interested in this position?",
            "Why do you want to work at this company?",
            "What are your strengths?",
            "What are your weaknesses?",
            "Describe a challenging project you worked on",
            "Where do you see yourself in 5 years?",
            "Why are you leaving your current position?",
            "What motivates you?",
            "Describe your ideal work environment",
            "What makes you a good fit for this role?"
        ]
    
    async def save_response_for_reuse(self, question: str, response: str) -> None:
        """Save generated response for future reuse."""
        try:
            # This could be implemented to store common responses
            # for reuse in future applications
            logger.info(f"Response saved for question type: {question[:30]}...")
            
        except Exception as e:
            logger.error(f"Failed to save response: {e}")


class QuestionClassifier:
    """Classifies application questions to determine if AI can handle them."""
    
    COMMON_QUESTIONS = {
        "motivation": [
            "why are you interested",
            "why do you want to work",
            "what motivates you",
            "why this position",
            "why this company"
        ],
        "experience": [
            "describe your experience",
            "tell us about a project",
            "challenging situation",
            "accomplishment",
            "achievement"
        ],
        "skills": [
            "what are your strengths",
            "technical skills",
            "programming languages",
            "tools and technologies"
        ],
        "career": [
            "where do you see yourself",
            "career goals",
            "future plans",
            "long term goals"
        ],
        "personal": [
            "what are your weaknesses",
            "describe yourself",
            "work style",
            "ideal work environment"
        ]
    }
    
    @classmethod
    def classify_question(cls, question: str) -> Optional[str]:
        """Classify question type."""
        question_lower = question.lower()
        
        for category, keywords in cls.COMMON_QUESTIONS.items():
            for keyword in keywords:
                if keyword in question_lower:
                    return category
        
        return None
    
    @classmethod
    def can_ai_handle(cls, question: str) -> bool:
        """Check if AI can handle this question type."""
        # AI can handle most common question types
        category = cls.classify_question(question)
        return category is not None
    
    @classmethod
    def requires_manual_input(cls, question: str) -> bool:
        """Check if question requires manual input."""
        manual_keywords = [
            "upload",
            "attach",
            "file",
            "document",
            "portfolio",
            "specific date",
            "exact number",
            "reference contact"
        ]
        
        question_lower = question.lower()
        return any(keyword in question_lower for keyword in manual_keywords)
