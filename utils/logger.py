"""
Comprehensive logging system for the job application automation.
"""

import sys
from pathlib import Path
from loguru import logger

from config.settings import LOGS_DIR, LOGGING_CONFIG


def setup_logging(debug: bool = False) -> None:
    """Setup logging configuration."""
    
    # Remove default handler
    logger.remove()
    
    # Determine log level
    log_level = "DEBUG" if debug else LOGGING_CONFIG["level"]
    
    # Console handler with colors
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
        colorize=True
    )
    
    # File handler for all logs
    logger.add(
        LOGS_DIR / "job_agent.log",
        level="DEBUG",
        format=LOGGING_CONFIG["format"],
        rotation=LOGGING_CONFIG["rotation"],
        retention=LOGGING_CONFIG["retention"],
        compression=LOGGING_CONFIG["compression"],
        enqueue=True
    )
    
    # Separate file for errors only
    logger.add(
        LOGS_DIR / "errors.log",
        level="ERROR",
        format=LOGGING_CONFIG["format"],
        rotation="1 day",
        retention="1 month",
        compression="gz",
        enqueue=True
    )
    
    # Application-specific log file
    logger.add(
        LOGS_DIR / "applications.log",
        level="INFO",
        format=LOGGING_CONFIG["format"],
        rotation="1 day",
        retention="3 months",
        compression="gz",
        enqueue=True,
        filter=lambda record: "APPLICATION" in record["extra"]
    )
    
    logger.info("Logging system initialized")


def get_application_logger():
    """Get logger specifically for application tracking."""
    return logger.bind(APPLICATION=True)


def log_application_attempt(job_title: str, company: str, status: str, details: str = "") -> None:
    """Log a job application attempt."""
    app_logger = get_application_logger()
    app_logger.info(f"APPLICATION | {company} | {job_title} | {status} | {details}")


def log_error_with_context(error: Exception, context: dict) -> None:
    """Log error with additional context."""
    logger.error(f"Error: {error}")
    logger.error(f"Context: {context}")


def log_browser_action(action: str, element: str = "", success: bool = True) -> None:
    """Log browser automation actions."""
    status = "SUCCESS" if success else "FAILED"
    logger.debug(f"BROWSER | {action} | {element} | {status}")


def log_notion_operation(operation: str, details: str = "", success: bool = True) -> None:
    """Log Notion API operations."""
    status = "SUCCESS" if success else "FAILED"
    logger.info(f"NOTION | {operation} | {details} | {status}")


def log_job_search(portal: str, keywords: str, results_count: int) -> None:
    """Log job search operations."""
    logger.info(f"JOB_SEARCH | {portal} | Keywords: {keywords} | Results: {results_count}")


def log_setup_step(step: str, success: bool = True) -> None:
    """Log setup process steps."""
    status = "COMPLETED" if success else "FAILED"
    logger.info(f"SETUP | {step} | {status}")


def log_daily_summary(applications_count: int, errors_count: int, new_jobs_count: int) -> None:
    """Log daily automation summary."""
    logger.info(f"DAILY_SUMMARY | Applications: {applications_count} | Errors: {errors_count} | New Jobs: {new_jobs_count}")


# Custom log levels for specific events
logger.level("APPLICATION", no=25, color="<blue>")
logger.level("JOB_FOUND", no=22, color="<green>")
logger.level("AUTOMATION", no=21, color="<yellow>")
