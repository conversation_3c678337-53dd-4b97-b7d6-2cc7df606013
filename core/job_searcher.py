"""
Job search functionality across multiple platforms.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from loguru import logger

from automation.browser_manager import BrowserManager
from utils.logger import log_job_search


class JobSearcher:
    """Handles job searching across multiple platforms."""
    
    def __init__(self):
        """Initialize job searcher."""
        self.browser_manager = None
        
    async def search_all_platforms(
        self, 
        keywords: List[str], 
        locations: List[str],
        browser_manager: BrowserManager
    ) -> List[Dict[str, Any]]:
        """Search for jobs across all configured platforms."""
        self.browser_manager = browser_manager
        all_jobs = []
        
        try:
            # Search LinkedIn
            linkedin_jobs = await self.search_linkedin(keywords, locations)
            all_jobs.extend(linkedin_jobs)
            
            # Search Indeed
            indeed_jobs = await self.search_indeed(keywords, locations)
            all_jobs.extend(indeed_jobs)
            
            # Search Glassdoor (if enabled)
            # glassdoor_jobs = await self.search_glassdoor(keywords, locations)
            # all_jobs.extend(glassdoor_jobs)
            
            logger.info(f"Total jobs found across all platforms: {len(all_jobs)}")
            return all_jobs
            
        except Exception as e:
            logger.error(f"Job search failed: {e}")
            return []
    
    async def search_linkedin(self, keywords: List[str], locations: List[str]) -> List[Dict[str, Any]]:
        """Search for jobs on LinkedIn."""
        jobs = []
        
        try:
            # Navigate to LinkedIn Jobs
            await self.browser_manager.navigate_to("https://www.linkedin.com/jobs")
            
            for keyword in keywords[:3]:  # Limit to first 3 keywords
                for location in locations[:3]:  # Limit to first 3 locations
                    try:
                        keyword_jobs = await self._search_linkedin_keyword(keyword, location)
                        jobs.extend(keyword_jobs)
                        
                        # Add delay between searches
                        await asyncio.sleep(5)
                        
                    except Exception as e:
                        logger.error(f"LinkedIn search failed for {keyword} in {location}: {e}")
            
            log_job_search("LinkedIn", f"{len(keywords)} keywords", len(jobs))
            return jobs
            
        except Exception as e:
            logger.error(f"LinkedIn search failed: {e}")
            return []
    
    async def _search_linkedin_keyword(self, keyword: str, location: str) -> List[Dict[str, Any]]:
        """Search LinkedIn for specific keyword and location."""
        jobs = []
        
        try:
            # Fill search form
            keyword_input = "input[aria-label='Search by title, skill, or company']"
            location_input = "input[aria-label='City, state, or zip code']"
            search_button = "button[aria-label='Search']"
            
            # Clear and fill keyword
            await self.browser_manager.type_text(keyword_input, keyword, clear_first=True)
            await asyncio.sleep(1)
            
            # Clear and fill location
            await self.browser_manager.type_text(location_input, location, clear_first=True)
            await asyncio.sleep(1)
            
            # Click search
            await self.browser_manager.click_element(search_button)
            await asyncio.sleep(3)
            
            # Apply filters for recent jobs
            await self._apply_linkedin_filters()
            
            # Extract job listings
            jobs = await self._extract_linkedin_jobs()
            
            logger.info(f"Found {len(jobs)} jobs for '{keyword}' in '{location}'")
            return jobs
            
        except Exception as e:
            logger.error(f"LinkedIn keyword search failed: {e}")
            return []
    
    async def _apply_linkedin_filters(self) -> None:
        """Apply filters for LinkedIn job search."""
        try:
            # Click on Date Posted filter
            date_filter = "button[aria-label='Date posted filter. Clicking this button displays all Date posted filter options.']"
            if await self.browser_manager.wait_for_element(date_filter, timeout=5):
                await self.browser_manager.click_element(date_filter)
                await asyncio.sleep(1)
                
                # Select "Past 24 hours" or "Past week"
                past_week = "input[value='r604800']"  # Past week
                if await self.browser_manager.wait_for_element(past_week, timeout=3):
                    await self.browser_manager.click_element(past_week)
                    await asyncio.sleep(1)
                
                # Apply filter
                apply_button = "button[aria-label='Apply current filter to show results']"
                await self.browser_manager.click_element(apply_button)
                await asyncio.sleep(2)
            
            # Apply Experience Level filter
            experience_filter = "button[aria-label='Experience level filter. Clicking this button displays all Experience level filter options.']"
            if await self.browser_manager.wait_for_element(experience_filter, timeout=5):
                await self.browser_manager.click_element(experience_filter)
                await asyncio.sleep(1)
                
                # Select Internship and Entry level
                internship = "input[value='1']"  # Internship
                entry_level = "input[value='2']"  # Entry level
                
                if await self.browser_manager.wait_for_element(internship, timeout=3):
                    await self.browser_manager.click_element(internship)
                
                if await self.browser_manager.wait_for_element(entry_level, timeout=3):
                    await self.browser_manager.click_element(entry_level)
                
                # Apply filter
                apply_button = "button[aria-label='Apply current filter to show results']"
                await self.browser_manager.click_element(apply_button)
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.debug(f"Failed to apply LinkedIn filters: {e}")
    
    async def _extract_linkedin_jobs(self) -> List[Dict[str, Any]]:
        """Extract job listings from LinkedIn search results."""
        jobs = []
        
        try:
            # Wait for job listings to load
            job_cards = "div[data-view-name='job-card']"
            await self.browser_manager.wait_for_element(job_cards, timeout=10)
            
            # Get all job card elements
            job_elements = await self.browser_manager.page.query_selector_all(job_cards)
            
            for i, job_element in enumerate(job_elements[:10]):  # Limit to first 10 jobs
                try:
                    job_data = await self._extract_job_data(job_element)
                    if job_data:
                        jobs.append(job_data)
                        
                except Exception as e:
                    logger.debug(f"Failed to extract job {i}: {e}")
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to extract LinkedIn jobs: {e}")
            return []
    
    async def _extract_job_data(self, job_element) -> Optional[Dict[str, Any]]:
        """Extract data from a single job card element."""
        try:
            # Extract job title
            title_element = await job_element.query_selector("h3 a")
            title = await title_element.text_content() if title_element else "Unknown"
            
            # Extract company name
            company_element = await job_element.query_selector("h4 a")
            company = await company_element.text_content() if company_element else "Unknown"
            
            # Extract location
            location_element = await job_element.query_selector("span.job-search-card__location")
            location = await location_element.text_content() if location_element else "Unknown"
            
            # Extract job URL
            link_element = await job_element.query_selector("h3 a")
            job_url = await link_element.get_attribute("href") if link_element else ""
            
            # Extract posting date
            date_element = await job_element.query_selector("time")
            posted_date = await date_element.get_attribute("datetime") if date_element else ""
            
            # Extract salary (if available)
            salary_element = await job_element.query_selector("span.job-search-card__salary-info")
            salary = await salary_element.text_content() if salary_element else ""
            
            job_data = {
                "title": title.strip(),
                "company": company.strip(),
                "location": location.strip(),
                "url": f"https://www.linkedin.com{job_url}" if job_url.startswith("/") else job_url,
                "salary": salary.strip(),
                "posted_date": posted_date,
                "source": "LinkedIn",
                "extracted_at": datetime.now().isoformat()
            }
            
            return job_data
            
        except Exception as e:
            logger.debug(f"Failed to extract job data: {e}")
            return None
    
    async def search_indeed(self, keywords: List[str], locations: List[str]) -> List[Dict[str, Any]]:
        """Search for jobs on Indeed."""
        jobs = []
        
        try:
            # Navigate to Indeed
            await self.browser_manager.navigate_to("https://www.indeed.com")
            
            for keyword in keywords[:2]:  # Limit searches
                for location in locations[:2]:
                    try:
                        keyword_jobs = await self._search_indeed_keyword(keyword, location)
                        jobs.extend(keyword_jobs)
                        
                        # Add delay between searches
                        await asyncio.sleep(5)
                        
                    except Exception as e:
                        logger.error(f"Indeed search failed for {keyword} in {location}: {e}")
            
            log_job_search("Indeed", f"{len(keywords)} keywords", len(jobs))
            return jobs
            
        except Exception as e:
            logger.error(f"Indeed search failed: {e}")
            return []
    
    async def _search_indeed_keyword(self, keyword: str, location: str) -> List[Dict[str, Any]]:
        """Search Indeed for specific keyword and location."""
        jobs = []
        
        try:
            # Fill search form
            what_input = "input[name='q']"
            where_input = "input[name='l']"
            search_button = "button[type='submit']"
            
            # Clear and fill what field
            await self.browser_manager.type_text(what_input, keyword, clear_first=True)
            await asyncio.sleep(1)
            
            # Clear and fill where field
            await self.browser_manager.type_text(where_input, location, clear_first=True)
            await asyncio.sleep(1)
            
            # Click search
            await self.browser_manager.click_element(search_button)
            await asyncio.sleep(3)
            
            # Extract job listings
            jobs = await self._extract_indeed_jobs()
            
            logger.info(f"Found {len(jobs)} jobs on Indeed for '{keyword}' in '{location}'")
            return jobs
            
        except Exception as e:
            logger.error(f"Indeed keyword search failed: {e}")
            return []
    
    async def _extract_indeed_jobs(self) -> List[Dict[str, Any]]:
        """Extract job listings from Indeed search results."""
        jobs = []
        
        try:
            # Wait for job listings
            job_cards = "div[data-jk]"
            await self.browser_manager.wait_for_element(job_cards, timeout=10)
            
            # Get job elements
            job_elements = await self.browser_manager.page.query_selector_all(job_cards)
            
            for i, job_element in enumerate(job_elements[:10]):  # Limit to first 10
                try:
                    job_data = await self._extract_indeed_job_data(job_element)
                    if job_data:
                        jobs.append(job_data)
                        
                except Exception as e:
                    logger.debug(f"Failed to extract Indeed job {i}: {e}")
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to extract Indeed jobs: {e}")
            return []
    
    async def _extract_indeed_job_data(self, job_element) -> Optional[Dict[str, Any]]:
        """Extract data from Indeed job card."""
        try:
            # Extract job title
            title_element = await job_element.query_selector("h2 a span")
            title = await title_element.text_content() if title_element else "Unknown"
            
            # Extract company
            company_element = await job_element.query_selector("span.companyName a")
            if not company_element:
                company_element = await job_element.query_selector("span.companyName")
            company = await company_element.text_content() if company_element else "Unknown"
            
            # Extract location
            location_element = await job_element.query_selector("div.companyLocation")
            location = await location_element.text_content() if location_element else "Unknown"
            
            # Extract job URL
            link_element = await job_element.query_selector("h2 a")
            job_url = await link_element.get_attribute("href") if link_element else ""
            
            # Extract salary
            salary_element = await job_element.query_selector("span.salaryText")
            salary = await salary_element.text_content() if salary_element else ""
            
            job_data = {
                "title": title.strip(),
                "company": company.strip(),
                "location": location.strip(),
                "url": f"https://www.indeed.com{job_url}" if job_url.startswith("/") else job_url,
                "salary": salary.strip(),
                "source": "Indeed",
                "extracted_at": datetime.now().isoformat()
            }
            
            return job_data
            
        except Exception as e:
            logger.debug(f"Failed to extract Indeed job data: {e}")
            return None
