"""
Main application processor that orchestrates the job application workflow.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from config.config_manager import ConfigManager
from automation.browser_manager import BrowserManager
from automation.linkedin_automation import LinkedInAutomation
from notion_integration.notion_client import NotionClient
from core.job_searcher import JobSearcher
from core.duplicate_detector import DuplicateDetector
from utils.logger import log_application_attempt, log_daily_summary


class ApplicationProcessor:
    """Main processor for automated job applications."""
    
    def __init__(self, dry_run: bool = False, max_applications: int = 10):
        """Initialize application processor."""
        self.dry_run = dry_run
        self.max_applications = max_applications
        self.config_manager = ConfigManager()
        self.browser_manager = None
        self.notion_client = NotionClient()
        self.job_searcher = JobSearcher()
        self.duplicate_detector = DuplicateDetector()
        
        # Statistics
        self.stats = {
            "applications_submitted": 0,
            "applications_failed": 0,
            "jobs_found": 0,
            "duplicates_skipped": 0,
            "errors": []
        }
    
    async def run(self) -> Dict[str, Any]:
        """Run the complete job application process."""
        logger.info(f"Starting job application process (dry_run={self.dry_run})")
        
        try:
            # Initialize browser
            await self._initialize_browser()
            
            # Get user data and preferences
            user_data = self.config_manager.get_user_data()
            job_preferences = self.config_manager.get_job_preferences()
            
            if not user_data or not job_preferences:
                raise ValueError("User data or job preferences not configured")
            
            # Search for jobs
            jobs = await self._search_jobs(job_preferences)
            self.stats["jobs_found"] = len(jobs)
            
            if not jobs:
                logger.info("No new jobs found")
                return self.stats
            
            logger.info(f"Found {len(jobs)} potential jobs")
            
            # Process applications
            await self._process_applications(jobs, user_data)
            
            # Log daily summary
            log_daily_summary(
                self.stats["applications_submitted"],
                len(self.stats["errors"]),
                self.stats["jobs_found"]
            )
            
            return self.stats
            
        except Exception as e:
            logger.error(f"Application process failed: {e}")
            self.stats["errors"].append(str(e))
            raise
        finally:
            await self._cleanup()
    
    async def _initialize_browser(self) -> None:
        """Initialize browser for automation."""
        try:
            self.browser_manager = BrowserManager(headless=self.dry_run)
            await self.browser_manager.start_browser()
            logger.info("Browser initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def _search_jobs(self, job_preferences: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for jobs across all configured portals."""
        all_jobs = []
        
        try:
            # Search LinkedIn
            linkedin_jobs = await self._search_linkedin(job_preferences)
            all_jobs.extend(linkedin_jobs)
            
            # TODO: Add other job portals (Indeed, Glassdoor)
            # indeed_jobs = await self._search_indeed(job_preferences)
            # all_jobs.extend(indeed_jobs)
            
            # Remove duplicates
            unique_jobs = self.duplicate_detector.remove_duplicate_jobs(all_jobs)
            logger.info(f"Found {len(unique_jobs)} unique jobs after deduplication")
            
            return unique_jobs
            
        except Exception as e:
            logger.error(f"Job search failed: {e}")
            self.stats["errors"].append(f"Job search error: {e}")
            return []
    
    async def _search_linkedin(self, job_preferences: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for jobs on LinkedIn."""
        try:
            linkedin_automation = LinkedInAutomation(self.browser_manager)
            
            # Login to LinkedIn
            user_data = self.config_manager.get_user_data()
            linkedin_email = user_data.get("email")
            linkedin_password = user_data.get("linkedin_password")  # This should be collected during setup
            
            if not linkedin_email:
                logger.warning("LinkedIn credentials not configured")
                return []
            
            # For now, we'll skip login and focus on job search
            # In production, you'd need to handle LinkedIn login securely
            
            jobs = await linkedin_automation.search_jobs(
                keywords=job_preferences.get("keywords", []),
                locations=job_preferences.get("locations", []),
                experience_levels=job_preferences.get("experience_levels", [])
            )
            
            logger.info(f"Found {len(jobs)} jobs on LinkedIn")
            return jobs
            
        except Exception as e:
            logger.error(f"LinkedIn search failed: {e}")
            return []
    
    async def _process_applications(self, jobs: List[Dict[str, Any]], user_data: Dict[str, Any]) -> None:
        """Process job applications."""
        applications_count = 0
        
        for job in jobs:
            if applications_count >= self.max_applications:
                logger.info(f"Reached maximum applications limit: {self.max_applications}")
                break
            
            try:
                # Check for duplicates in Notion
                if await self._is_duplicate_application(job):
                    self.stats["duplicates_skipped"] += 1
                    continue
                
                # Apply to job
                success = await self._apply_to_job(job, user_data)
                
                if success:
                    self.stats["applications_submitted"] += 1
                    applications_count += 1
                    
                    # Log to Notion
                    await self._log_application_to_notion(job, "Applied")
                    
                    log_application_attempt(
                        job.get("title", "Unknown"),
                        job.get("company", "Unknown"),
                        "SUCCESS"
                    )
                else:
                    self.stats["applications_failed"] += 1
                    
                    # Log failed application
                    await self._log_application_to_notion(job, "Manual Action Needed")
                    
                    log_application_attempt(
                        job.get("title", "Unknown"),
                        job.get("company", "Unknown"),
                        "FAILED"
                    )
                
                # Add delay between applications
                await asyncio.sleep(30)  # 30 second delay
                
            except Exception as e:
                logger.error(f"Failed to process application for {job.get('title', 'Unknown')}: {e}")
                self.stats["errors"].append(f"Application error: {e}")
                self.stats["applications_failed"] += 1
    
    async def _is_duplicate_application(self, job: Dict[str, Any]) -> bool:
        """Check if we've already applied to this job."""
        try:
            return await self.notion_client.check_duplicate_application(
                job.get("company", ""),
                job.get("title", "")
            )
        except Exception as e:
            logger.error(f"Duplicate check failed: {e}")
            return False
    
    async def _apply_to_job(self, job: Dict[str, Any], user_data: Dict[str, Any]) -> bool:
        """Apply to a specific job."""
        if self.dry_run:
            logger.info(f"DRY RUN: Would apply to {job.get('title')} at {job.get('company')}")
            return True
        
        try:
            source = job.get("source", "").lower()
            
            if source == "linkedin":
                return await self._apply_linkedin_job(job, user_data)
            elif source == "indeed":
                return await self._apply_indeed_job(job, user_data)
            else:
                logger.warning(f"Unknown job source: {source}")
                return False
                
        except Exception as e:
            logger.error(f"Job application failed: {e}")
            return False
    
    async def _apply_linkedin_job(self, job: Dict[str, Any], user_data: Dict[str, Any]) -> bool:
        """Apply to LinkedIn job."""
        try:
            linkedin_automation = LinkedInAutomation(self.browser_manager)
            
            # Navigate to job
            await self.browser_manager.navigate_to(job.get("url", ""))
            
            # Check if it's Easy Apply
            if await linkedin_automation.is_easy_apply():
                return await linkedin_automation.apply_easy_apply(job, user_data)
            else:
                # Handle external application
                return await linkedin_automation.apply_external(job, user_data)
                
        except Exception as e:
            logger.error(f"LinkedIn application failed: {e}")
            return False
    
    async def _apply_indeed_job(self, job: Dict[str, Any], user_data: Dict[str, Any]) -> bool:
        """Apply to Indeed job."""
        # TODO: Implement Indeed application logic
        logger.info("Indeed application not yet implemented")
        return False
    
    async def _log_application_to_notion(self, job: Dict[str, Any], status: str) -> None:
        """Log application to Notion database."""
        try:
            if self.dry_run:
                logger.info(f"DRY RUN: Would log to Notion: {job.get('title')} - {status}")
                return
            
            job_data = {
                "title": job.get("title", ""),
                "company": job.get("company", ""),
                "location": job.get("location", ""),
                "salary": job.get("salary", ""),
                "status": status,
                "source": job.get("source", ""),
                "url": job.get("url", ""),
                "description": job.get("description", ""),
                "date": datetime.now().isoformat()
            }
            
            await self.notion_client.add_job_application(job_data)
            
        except Exception as e:
            logger.error(f"Failed to log application to Notion: {e}")
    
    async def _cleanup(self) -> None:
        """Cleanup resources."""
        try:
            if self.browser_manager:
                await self.browser_manager.close_browser()
            logger.info("Cleanup completed")
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return self.stats.copy()
