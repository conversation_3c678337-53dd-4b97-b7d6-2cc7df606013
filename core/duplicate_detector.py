"""
Duplicate job detection and filtering.
"""

import hashlib
from typing import Dict, Any, List, Set
from difflib import Se<PERSON>Matcher
from loguru import logger


class DuplicateDetector:
    """Detects and removes duplicate job postings."""
    
    def __init__(self, similarity_threshold: float = 0.85):
        """Initialize duplicate detector."""
        self.similarity_threshold = similarity_threshold
        self.seen_jobs: Set[str] = set()
        
    def remove_duplicate_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate jobs from list."""
        unique_jobs = []
        job_signatures = set()
        
        for job in jobs:
            try:
                # Create job signature
                signature = self._create_job_signature(job)
                
                # Check if we've seen this exact job
                if signature in job_signatures:
                    logger.debug(f"Exact duplicate found: {job.get('title', 'Unknown')} at {job.get('company', 'Unknown')}")
                    continue
                
                # Check for similar jobs
                if self._is_similar_job(job, unique_jobs):
                    logger.debug(f"Similar job found: {job.get('title', 'Unknown')} at {job.get('company', 'Unknown')}")
                    continue
                
                # Add to unique jobs
                unique_jobs.append(job)
                job_signatures.add(signature)
                
            except Exception as e:
                logger.error(f"Error processing job for duplicates: {e}")
                # Include job if we can't process it
                unique_jobs.append(job)
        
        logger.info(f"Removed {len(jobs) - len(unique_jobs)} duplicate jobs")
        return unique_jobs
    
    def _create_job_signature(self, job: Dict[str, Any]) -> str:
        """Create unique signature for job."""
        # Normalize data
        title = self._normalize_text(job.get("title", ""))
        company = self._normalize_text(job.get("company", ""))
        location = self._normalize_text(job.get("location", ""))
        
        # Create signature
        signature_data = f"{title}|{company}|{location}"
        return hashlib.md5(signature_data.encode()).hexdigest()
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison."""
        if not text:
            return ""
        
        # Convert to lowercase and remove extra whitespace
        normalized = " ".join(text.lower().split())
        
        # Remove common variations
        replacements = {
            "software engineer": "swe",
            "software engineering": "swe",
            "software developer": "swe",
            "software development": "swe",
            "intern": "internship",
            "entry level": "entry-level",
            "entry-level": "entry level"
        }
        
        for old, new in replacements.items():
            normalized = normalized.replace(old, new)
        
        return normalized
    
    def _is_similar_job(self, job: Dict[str, Any], existing_jobs: List[Dict[str, Any]]) -> bool:
        """Check if job is similar to existing jobs."""
        job_title = self._normalize_text(job.get("title", ""))
        job_company = self._normalize_text(job.get("company", ""))
        
        for existing_job in existing_jobs:
            existing_title = self._normalize_text(existing_job.get("title", ""))
            existing_company = self._normalize_text(existing_job.get("company", ""))
            
            # Check if same company and similar title
            if job_company == existing_company:
                title_similarity = self._calculate_similarity(job_title, existing_title)
                if title_similarity >= self.similarity_threshold:
                    return True
            
            # Check if very similar title and company
            title_similarity = self._calculate_similarity(job_title, existing_title)
            company_similarity = self._calculate_similarity(job_company, existing_company)
            
            if (title_similarity >= 0.9 and company_similarity >= 0.8):
                return True
        
        return False
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts."""
        if not text1 or not text2:
            return 0.0
        
        return SequenceMatcher(None, text1, text2).ratio()
    
    def is_duplicate_by_url(self, job_url: str) -> bool:
        """Check if job URL has been seen before."""
        if not job_url:
            return False
        
        # Normalize URL
        normalized_url = self._normalize_url(job_url)
        
        if normalized_url in self.seen_jobs:
            return True
        
        self.seen_jobs.add(normalized_url)
        return False
    
    def _normalize_url(self, url: str) -> str:
        """Normalize URL for comparison."""
        if not url:
            return ""
        
        # Remove query parameters and fragments
        if "?" in url:
            url = url.split("?")[0]
        if "#" in url:
            url = url.split("#")[0]
        
        # Remove trailing slash
        url = url.rstrip("/")
        
        return url.lower()
    
    def add_applied_job(self, job: Dict[str, Any]) -> None:
        """Add job to applied jobs list to prevent reapplication."""
        signature = self._create_job_signature(job)
        self.seen_jobs.add(signature)
        
        # Also add URL if available
        job_url = job.get("url", "")
        if job_url:
            normalized_url = self._normalize_url(job_url)
            self.seen_jobs.add(normalized_url)
    
    def clear_cache(self) -> None:
        """Clear the cache of seen jobs."""
        self.seen_jobs.clear()
        logger.info("Duplicate detector cache cleared")
    
    def get_cache_size(self) -> int:
        """Get number of jobs in cache."""
        return len(self.seen_jobs)


class CompanyDuplicateTracker:
    """Track applications per company to avoid over-applying."""
    
    def __init__(self, max_applications_per_company: int = 3):
        """Initialize company tracker."""
        self.max_applications_per_company = max_applications_per_company
        self.company_applications: Dict[str, int] = {}
    
    def can_apply_to_company(self, company: str) -> bool:
        """Check if we can apply to this company."""
        if not company:
            return True
        
        normalized_company = self._normalize_company_name(company)
        current_count = self.company_applications.get(normalized_company, 0)
        
        return current_count < self.max_applications_per_company
    
    def record_application(self, company: str) -> None:
        """Record application to company."""
        if not company:
            return
        
        normalized_company = self._normalize_company_name(company)
        self.company_applications[normalized_company] = (
            self.company_applications.get(normalized_company, 0) + 1
        )
        
        logger.debug(f"Recorded application to {company} (total: {self.company_applications[normalized_company]})")
    
    def _normalize_company_name(self, company: str) -> str:
        """Normalize company name for comparison."""
        if not company:
            return ""
        
        # Convert to lowercase and remove extra whitespace
        normalized = " ".join(company.lower().split())
        
        # Remove common suffixes
        suffixes = ["inc", "inc.", "llc", "corp", "corporation", "ltd", "limited", "co", "company"]
        words = normalized.split()
        
        # Remove suffix if it's the last word
        if words and words[-1] in suffixes:
            words = words[:-1]
        
        return " ".join(words)
    
    def get_company_application_count(self, company: str) -> int:
        """Get number of applications to company."""
        normalized_company = self._normalize_company_name(company)
        return self.company_applications.get(normalized_company, 0)
    
    def get_all_company_counts(self) -> Dict[str, int]:
        """Get all company application counts."""
        return self.company_applications.copy()
    
    def reset_company_counts(self) -> None:
        """Reset all company application counts."""
        self.company_applications.clear()
        logger.info("Company application counts reset")
