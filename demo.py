#!/usr/bin/env python3
"""
Demo script to test the job application automation system.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import setup_logging
from config.config_manager import ConfigManager
from automation.browser_manager import BrowserManager


async def test_browser_automation():
    """Test browser automation functionality."""
    print("🌐 Testing browser automation...")
    
    try:
        # Initialize browser manager
        browser_manager = BrowserManager(headless=True)
        await browser_manager.start_browser()
        
        # Navigate to a test page
        await browser_manager.navigate_to("https://www.google.com")
        
        # Take a screenshot
        screenshot_path = await browser_manager.take_screenshot("demo_test.png")
        print(f"✅ Screenshot saved: {screenshot_path}")
        
        # Close browser
        await browser_manager.close_browser()
        
        print("✅ Browser automation test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Browser automation test failed: {e}")
        return False


def test_configuration():
    """Test configuration management."""
    print("⚙️  Testing configuration management...")
    
    try:
        config_manager = ConfigManager()
        
        # Test default config
        default_config = config_manager._get_default_config()
        print(f"✅ Default config loaded: {len(default_config)} sections")
        
        # Test job preferences
        job_prefs = config_manager.get_job_preferences()
        print(f"✅ Job preferences loaded: {len(job_prefs.get('keywords', []))} keywords")
        
        print("✅ Configuration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_encryption():
    """Test encryption functionality."""
    print("🔐 Testing encryption...")
    
    try:
        from config.encryption import EncryptionManager
        
        # Test data
        test_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "secret": "This is a secret message"
        }
        
        # Test encryption/decryption
        encryption_manager = EncryptionManager()
        password = "test_password_123"
        
        # Encrypt
        encrypted_data = encryption_manager.encrypt_data(test_data, password)
        print(f"✅ Data encrypted: {len(encrypted_data)} bytes")
        
        # Decrypt
        decrypted_data = encryption_manager.decrypt_data(encrypted_data, password)
        print(f"✅ Data decrypted: {len(decrypted_data)} fields")
        
        # Verify data integrity
        if decrypted_data == test_data:
            print("✅ Data integrity verified!")
        else:
            print("❌ Data integrity check failed!")
            return False
        
        print("✅ Encryption test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        return False


def test_notion_client():
    """Test Notion client (without API key)."""
    print("📊 Testing Notion client...")
    
    try:
        from notion_integration.notion_client import NotionClient
        
        # Initialize client (will warn about missing API key)
        notion_client = NotionClient()
        
        # Test connection (will fail without API key, but shouldn't crash)
        connection_ok = notion_client.test_connection()
        print(f"✅ Notion client initialized (connection: {'OK' if connection_ok else 'No API key'})")
        
        print("✅ Notion client test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Notion client test failed: {e}")
        return False


def test_ai_responder():
    """Test AI responder (without API key)."""
    print("🤖 Testing AI responder...")
    
    try:
        from utils.ai_responder import AIResponder, QuestionClassifier
        
        # Initialize responder
        ai_responder = AIResponder()
        print(f"✅ AI responder initialized (available: {ai_responder.is_available()})")
        
        # Test question classification
        test_questions = [
            "Why are you interested in this position?",
            "What are your technical skills?",
            "Upload your portfolio",
            "Random question that doesn't fit"
        ]
        
        for question in test_questions:
            category = QuestionClassifier.classify_question(question)
            can_handle = QuestionClassifier.can_ai_handle(question)
            print(f"  Question: '{question[:30]}...' -> Category: {category}, Can handle: {can_handle}")
        
        print("✅ AI responder test passed!")
        return True
        
    except Exception as e:
        print(f"❌ AI responder test failed: {e}")
        return False


async def run_demo():
    """Run all demo tests."""
    print("🚀 Job Application Automation System - Demo")
    print("=" * 60)
    
    # Setup logging
    setup_logging(debug=True)
    
    tests = [
        ("Configuration Management", test_configuration),
        ("Encryption System", test_encryption),
        ("Notion Client", test_notion_client),
        ("AI Responder", test_ai_responder),
        ("Browser Automation", test_browser_automation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * 40)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Demo Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All demo tests passed! The system is ready to use.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and add your API keys:")
        print("   - NOTION_API_KEY (required)")
        print("   - OPENAI_API_KEY or ANTHROPIC_API_KEY (optional)")
        print("2. Run setup: python3 main.py setup")
        print("3. Test with dry run: python3 main.py apply --dry-run")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = asyncio.run(run_demo())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Demo cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        sys.exit(1)
