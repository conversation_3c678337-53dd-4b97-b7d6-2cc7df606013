#!/usr/bin/env python3
"""
Job Application Automation System
Main entry point for the intelligent job application agent.
"""

import asyncio
import sys
from pathlib import Path
import click
from loguru import logger

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.config_manager import ConfigManager
from setup.initial_setup import InitialSetup
from core.application_processor import ApplicationProcessor
from utils.scheduler import JobScheduler
from utils.logger import setup_logging


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug logging')
def cli(debug):
    """Job Application Automation System - Intelligent job application agent."""
    setup_logging(debug=debug)


@cli.command()
def setup():
    """Run the initial setup process to configure user data and preferences."""
    click.echo("🚀 Starting Job Application Agent Setup...")
    
    try:
        setup_manager = InitialSetup()
        asyncio.run(setup_manager.run_setup())
        click.echo("✅ Setup completed successfully!")
        click.echo("You can now run 'python main.py apply' to start applying to jobs.")
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        click.echo(f"❌ Setup failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--dry-run', is_flag=True, help='Run without actually applying to jobs')
@click.option('--max-applications', default=10, help='Maximum number of applications per run')
def apply(dry_run, max_applications):
    """Run the job application process."""
    click.echo("🔍 Starting job application process...")
    
    try:
        # Check if setup has been completed
        config_manager = ConfigManager()
        if not config_manager.is_setup_complete():
            click.echo("❌ Setup not completed. Please run 'python main.py setup' first.")
            sys.exit(1)
        
        processor = ApplicationProcessor(
            dry_run=dry_run,
            max_applications=max_applications
        )
        asyncio.run(processor.run())
        
        if dry_run:
            click.echo("✅ Dry run completed successfully!")
        else:
            click.echo("✅ Job application process completed!")
            
    except Exception as e:
        logger.error(f"Application process failed: {e}")
        click.echo(f"❌ Application process failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--time', default='08:00', help='Daily run time (HH:MM format)')
def schedule(time):
    """Schedule daily job applications."""
    click.echo(f"⏰ Scheduling daily job applications at {time}...")
    
    try:
        scheduler = JobScheduler()
        scheduler.schedule_daily_run(time)
        click.echo("✅ Scheduler started. Press Ctrl+C to stop.")
        scheduler.run()
    except KeyboardInterrupt:
        click.echo("\n⏹️  Scheduler stopped.")
    except Exception as e:
        logger.error(f"Scheduler failed: {e}")
        click.echo(f"❌ Scheduler failed: {e}")
        sys.exit(1)


@cli.command()
def status():
    """Check the status of the job application system."""
    click.echo("📊 Checking system status...")
    
    try:
        config_manager = ConfigManager()
        
        # Check setup status
        if config_manager.is_setup_complete():
            click.echo("✅ Setup: Complete")
            
            # Show basic stats
            user_data = config_manager.get_user_data()
            click.echo(f"👤 User: {user_data.get('name', 'Unknown')}")
            click.echo(f"📧 Email: {user_data.get('email', 'Unknown')}")
            
            # Check Notion connection
            from notion_integration.notion_client import NotionClient
            notion_client = NotionClient()
            if notion_client.test_connection():
                click.echo("✅ Notion: Connected")
            else:
                click.echo("❌ Notion: Connection failed")
                
        else:
            click.echo("❌ Setup: Not completed")
            click.echo("Run 'python main.py setup' to get started.")
            
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        click.echo(f"❌ Status check failed: {e}")


@cli.command()
def config():
    """Show current configuration (without sensitive data)."""
    click.echo("⚙️  Current Configuration:")
    
    try:
        config_manager = ConfigManager()
        if not config_manager.is_setup_complete():
            click.echo("❌ Setup not completed.")
            return
            
        config_data = config_manager.get_config_summary()
        for key, value in config_data.items():
            click.echo(f"{key}: {value}")
            
    except Exception as e:
        logger.error(f"Config display failed: {e}")
        click.echo(f"❌ Config display failed: {e}")


if __name__ == '__main__':
    cli()
