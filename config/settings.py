"""
Application settings and constants.
"""

import os
from pathlib import Path
from typing import Dict, List

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
CONFIG_FILE = DATA_DIR / "config.enc"
USER_DATA_FILE = DATA_DIR / "user_data.enc"

# Ensure directories exist
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Job search settings
DEFAULT_JOB_KEYWORDS = [
    "Software Engineer Intern",
    "Software Engineering Intern", 
    "Cloud Engineer Intern",
    "Cloud Engineering Intern",
    "Software Developer Intern",
    "Backend Engineer Intern",
    "Frontend Engineer Intern",
    "Full Stack Engineer Intern",
    "DevOps Engineer Intern",
    "Data Engineer Intern"
]

DEFAULT_LOCATIONS = [
    "San Francisco, CA",
    "San Jose, CA", 
    "Mountain View, CA",
    "Palo Alto, CA",
    "Sunnyvale, CA",
    "Redwood City, CA",
    "Remote",
    "United States"
]

# Job portals configuration
JOB_PORTALS = {
    "linkedin": {
        "name": "LinkedIn",
        "base_url": "https://www.linkedin.com",
        "jobs_url": "https://www.linkedin.com/jobs",
        "enabled": True,
        "priority": 1
    },
    "indeed": {
        "name": "Indeed", 
        "base_url": "https://www.indeed.com",
        "jobs_url": "https://www.indeed.com/jobs",
        "enabled": True,
        "priority": 2
    },
    "glassdoor": {
        "name": "Glassdoor",
        "base_url": "https://www.glassdoor.com",
        "jobs_url": "https://www.glassdoor.com/Job/jobs.htm",
        "enabled": False,  # Can be enabled later
        "priority": 3
    }
}

# Browser automation settings
BROWSER_CONFIG = {
    "headless": False,  # Set to True for production
    "window_size": (1920, 1080),
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "timeout": 30,
    "implicit_wait": 10,
    "page_load_timeout": 60
}

# Application limits and timing
APPLICATION_LIMITS = {
    "max_daily_applications": 50,
    "max_per_company": 3,
    "min_delay_between_applications": 30,  # seconds
    "max_delay_between_applications": 120,  # seconds
    "session_timeout": 3600  # 1 hour
}

# Notion database schema
NOTION_DATABASE_SCHEMA = {
    "Job Title": {"type": "title"},
    "Company": {"type": "rich_text"},
    "Location": {"type": "rich_text"},
    "Salary Range": {"type": "rich_text"},
    "Application Status": {
        "type": "select",
        "options": [
            {"name": "Applied", "color": "blue"},
            {"name": "Interview Scheduled", "color": "yellow"},
            {"name": "Rejected", "color": "red"},
            {"name": "Offer Received", "color": "green"},
            {"name": "Manual Action Needed", "color": "orange"},
            {"name": "Skipped", "color": "gray"}
        ]
    },
    "Source": {
        "type": "select", 
        "options": [
            {"name": "LinkedIn", "color": "blue"},
            {"name": "Indeed", "color": "green"},
            {"name": "Glassdoor", "color": "purple"},
            {"name": "Company Website", "color": "yellow"}
        ]
    },
    "Application Date": {"type": "date"},
    "Job URL": {"type": "url"},
    "Description": {"type": "rich_text"},
    "Notes": {"type": "rich_text"},
    "Follow Up Date": {"type": "date"}
}

# AI/LLM settings
AI_CONFIG = {
    "provider": "openai",  # or "anthropic"
    "model": "gpt-4-turbo-preview",
    "max_tokens": 500,
    "temperature": 0.7,
    "timeout": 30
}

# Logging configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    "rotation": "1 week",
    "retention": "1 month",
    "compression": "gz"
}

# Security settings
SECURITY_CONFIG = {
    "encryption_algorithm": "Fernet",
    "key_derivation_iterations": 100000,
    "salt_length": 32
}

# Environment variables
def get_env_var(key: str, default: str = None) -> str:
    """Get environment variable with optional default."""
    return os.getenv(key, default)

# Required environment variables
REQUIRED_ENV_VARS = [
    "NOTION_API_KEY",
    "OPENAI_API_KEY"  # or ANTHROPIC_API_KEY
]
