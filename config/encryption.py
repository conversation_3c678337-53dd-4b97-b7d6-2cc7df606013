"""
Data encryption and decryption utilities for secure storage.
"""

import os
import json
import base64
from typing import Dict, Any, Optional
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import P<PERSON><PERSON>DF2HMAC
from loguru import logger

from .settings import SECURITY_CONFIG


class EncryptionManager:
    """Handles encryption and decryption of sensitive data."""
    
    def __init__(self, password: Optional[str] = None):
        """Initialize encryption manager with password."""
        self.password = password
        self._key = None
        
    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password and salt."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=SECURITY_CONFIG["key_derivation_iterations"]
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def _get_or_create_key(self, password: str) -> <PERSON><PERSON><PERSON>:
        """Get or create encryption key."""
        if self._key is None:
            # Generate or load salt
            salt_file = "data/.salt"
            if os.path.exists(salt_file):
                with open(salt_file, "rb") as f:
                    salt = f.read()
            else:
                salt = os.urandom(SECURITY_CONFIG["salt_length"])
                os.makedirs("data", exist_ok=True)
                with open(salt_file, "wb") as f:
                    f.write(salt)
            
            key = self._derive_key(password, salt)
            self._key = Fernet(key)
        
        return self._key
    
    def encrypt_data(self, data: Dict[str, Any], password: str) -> bytes:
        """Encrypt dictionary data."""
        try:
            fernet = self._get_or_create_key(password)
            json_data = json.dumps(data, indent=2)
            encrypted_data = fernet.encrypt(json_data.encode())
            logger.debug("Data encrypted successfully")
            return encrypted_data
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: bytes, password: str) -> Dict[str, Any]:
        """Decrypt data back to dictionary."""
        try:
            fernet = self._get_or_create_key(password)
            decrypted_data = fernet.decrypt(encrypted_data)
            data = json.loads(decrypted_data.decode())
            logger.debug("Data decrypted successfully")
            return data
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise
    
    def encrypt_file(self, file_path: str, data: Dict[str, Any], password: str) -> None:
        """Encrypt data and save to file."""
        try:
            encrypted_data = self.encrypt_data(data, password)
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "wb") as f:
                f.write(encrypted_data)
            logger.info(f"Data encrypted and saved to {file_path}")
        except Exception as e:
            logger.error(f"Failed to encrypt file {file_path}: {e}")
            raise
    
    def decrypt_file(self, file_path: str, password: str) -> Dict[str, Any]:
        """Decrypt data from file."""
        try:
            if not os.path.exists(file_path):
                logger.warning(f"Encrypted file {file_path} does not exist")
                return {}
            
            with open(file_path, "rb") as f:
                encrypted_data = f.read()
            
            data = self.decrypt_data(encrypted_data, password)
            logger.info(f"Data decrypted from {file_path}")
            return data
        except Exception as e:
            logger.error(f"Failed to decrypt file {file_path}: {e}")
            raise
    
    def file_exists(self, file_path: str) -> bool:
        """Check if encrypted file exists."""
        return os.path.exists(file_path)
    
    @staticmethod
    def generate_password() -> str:
        """Generate a secure random password for encryption."""
        return base64.urlsafe_b64encode(os.urandom(32)).decode()
    
    def change_password(self, old_password: str, new_password: str, file_path: str) -> None:
        """Change encryption password for existing file."""
        try:
            # Decrypt with old password
            data = self.decrypt_file(file_path, old_password)
            
            # Reset key to force regeneration
            self._key = None
            
            # Encrypt with new password
            self.encrypt_file(file_path, data, new_password)
            
            logger.info("Password changed successfully")
        except Exception as e:
            logger.error(f"Failed to change password: {e}")
            raise


def get_master_password() -> str:
    """Get master password from environment or prompt user."""
    # Try environment variable first
    password = os.getenv("JOB_AGENT_MASTER_PASSWORD")
    if password:
        return password
    
    # For development, use a default password
    # In production, this should prompt the user
    import getpass
    try:
        password = getpass.getpass("Enter master password for data encryption: ")
        if not password:
            raise ValueError("Password cannot be empty")
        return password
    except KeyboardInterrupt:
        logger.info("Password entry cancelled")
        raise
    except Exception as e:
        logger.error(f"Failed to get password: {e}")
        raise
