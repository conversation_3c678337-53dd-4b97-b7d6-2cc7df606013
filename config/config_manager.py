"""
Configuration management system for secure data storage and retrieval.
"""

import os
from typing import Dict, Any, Optional, List
from pathlib import Path
from loguru import logger

from .encryption import Encryption<PERSON>anager, get_master_password
from .settings import (
    CONFIG_FILE, USER_DATA_FILE, DEFAULT_JOB_KEYWORDS, 
    DEFAULT_LOCATIONS, APPLICATION_LIMITS
)


class ConfigManager:
    """Manages application configuration and user data with encryption."""
    
    def __init__(self):
        """Initialize configuration manager."""
        self.encryption_manager = EncryptionManager()
        self._master_password = None
        
    def _get_master_password(self) -> str:
        """Get master password for encryption."""
        if self._master_password is None:
            self._master_password = get_master_password()
        return self._master_password
    
    def is_setup_complete(self) -> bool:
        """Check if initial setup has been completed."""
        return (
            self.encryption_manager.file_exists(str(CONFIG_FILE)) and
            self.encryption_manager.file_exists(str(USER_DATA_FILE))
        )
    
    def save_config(self, config_data: Dict[str, Any]) -> None:
        """Save application configuration."""
        try:
            password = self._get_master_password()
            self.encryption_manager.encrypt_file(str(CONFIG_FILE), config_data, password)
            logger.info("Configuration saved successfully")
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise
    
    def load_config(self) -> Dict[str, Any]:
        """Load application configuration."""
        try:
            password = self._get_master_password()
            config = self.encryption_manager.decrypt_file(str(CONFIG_FILE), password)
            logger.debug("Configuration loaded successfully")
            return config
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return self._get_default_config()
    
    def save_user_data(self, user_data: Dict[str, Any]) -> None:
        """Save user personal data."""
        try:
            password = self._get_master_password()
            self.encryption_manager.encrypt_file(str(USER_DATA_FILE), user_data, password)
            logger.info("User data saved successfully")
        except Exception as e:
            logger.error(f"Failed to save user data: {e}")
            raise
    
    def get_user_data(self) -> Dict[str, Any]:
        """Get user personal data."""
        try:
            password = self._get_master_password()
            user_data = self.encryption_manager.decrypt_file(str(USER_DATA_FILE), password)
            logger.debug("User data loaded successfully")
            return user_data
        except Exception as e:
            logger.error(f"Failed to load user data: {e}")
            return {}
    
    def get_job_preferences(self) -> Dict[str, Any]:
        """Get job search preferences."""
        config = self.load_config()
        return config.get("job_preferences", {
            "keywords": DEFAULT_JOB_KEYWORDS,
            "locations": DEFAULT_LOCATIONS,
            "experience_levels": ["Internship", "Entry level"],
            "job_types": ["Full-time", "Part-time", "Internship"],
            "remote_preference": "Remote, Hybrid, On-site",
            "salary_min": None,
            "salary_max": None
        })
    
    def get_application_settings(self) -> Dict[str, Any]:
        """Get application automation settings."""
        config = self.load_config()
        return config.get("application_settings", APPLICATION_LIMITS)
    
    def get_notion_config(self) -> Dict[str, Any]:
        """Get Notion integration configuration."""
        config = self.load_config()
        return config.get("notion_config", {})
    
    def get_browser_config(self) -> Dict[str, Any]:
        """Get browser automation configuration."""
        config = self.load_config()
        return config.get("browser_config", {})
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get AI/LLM configuration."""
        config = self.load_config()
        return config.get("ai_config", {})
    
    def update_config_section(self, section: str, data: Dict[str, Any]) -> None:
        """Update a specific section of configuration."""
        try:
            config = self.load_config()
            config[section] = data
            self.save_config(config)
            logger.info(f"Configuration section '{section}' updated")
        except Exception as e:
            logger.error(f"Failed to update config section '{section}': {e}")
            raise
    
    def get_config_summary(self) -> Dict[str, str]:
        """Get configuration summary without sensitive data."""
        try:
            config = self.load_config()
            user_data = self.get_user_data()
            
            summary = {
                "Setup Status": "Complete" if self.is_setup_complete() else "Incomplete",
                "User Name": user_data.get("name", "Not set"),
                "Email": user_data.get("email", "Not set"),
                "Resume File": "Uploaded" if user_data.get("resume_path") else "Not uploaded",
                "Job Keywords": f"{len(self.get_job_preferences().get('keywords', []))} keywords",
                "Target Locations": f"{len(self.get_job_preferences().get('locations', []))} locations",
                "Notion Integration": "Configured" if self.get_notion_config().get("api_key") else "Not configured",
                "Daily Application Limit": str(self.get_application_settings().get("max_daily_applications", "Not set"))
            }
            
            return summary
        except Exception as e:
            logger.error(f"Failed to get config summary: {e}")
            return {"Error": str(e)}
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration structure."""
        return {
            "job_preferences": {
                "keywords": DEFAULT_JOB_KEYWORDS,
                "locations": DEFAULT_LOCATIONS,
                "experience_levels": ["Internship", "Entry level"],
                "job_types": ["Full-time", "Part-time", "Internship"],
                "remote_preference": "Remote, Hybrid, On-site"
            },
            "application_settings": APPLICATION_LIMITS,
            "notion_config": {},
            "browser_config": {},
            "ai_config": {},
            "setup_completed": False,
            "created_at": None,
            "last_updated": None
        }
    
    def reset_configuration(self) -> None:
        """Reset all configuration (use with caution)."""
        try:
            if CONFIG_FILE.exists():
                CONFIG_FILE.unlink()
            if USER_DATA_FILE.exists():
                USER_DATA_FILE.unlink()
            
            # Remove salt file to force new key generation
            salt_file = Path("data/.salt")
            if salt_file.exists():
                salt_file.unlink()
                
            logger.warning("All configuration data has been reset")
        except Exception as e:
            logger.error(f"Failed to reset configuration: {e}")
            raise
