#!/usr/bin/env python3
"""
Final system check for the Job Application Automation System.
"""

import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_system_ready():
    """Check if the system is ready for use."""
    print("🔍 Final System Check")
    print("=" * 50)
    
    checks = []
    
    # Check 1: Python version
    version = sys.version_info
    if version.major == 3 and version.minor >= 9:
        checks.append(("Python Version", True, f"Python {version.major}.{version.minor}.{version.micro}"))
    else:
        checks.append(("Python Version", False, f"Python {version.major}.{version.minor}.{version.micro} - Need 3.9+"))
    
    # Check 2: Required files
    required_files = ["main.py", "requirements.txt", ".env", "README.md"]
    all_files_exist = all(Path(f).exists() for f in required_files)
    checks.append(("Required Files", all_files_exist, f"{sum(Path(f).exists() for f in required_files)}/{len(required_files)} files"))
    
    # Check 3: Environment variables
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            env_content = f.read()
        
        has_openai = "OPENAI_API_KEY=sk-proj-" in env_content
        has_notion_placeholder = "NOTION_API_KEY=your_notion_api_key_here" in env_content
        
        if has_openai:
            checks.append(("OpenAI API Key", True, "Configured"))
        else:
            checks.append(("OpenAI API Key", False, "Not found"))
        
        if has_notion_placeholder:
            checks.append(("Notion API Key", False, "Needs configuration"))
        else:
            checks.append(("Notion API Key", True, "Configured"))
    else:
        checks.append(("Environment File", False, ".env file missing"))
    
    # Check 4: Dependencies
    try:
        import click, loguru, cryptography, schedule, pydantic
        import aiofiles, requests, pandas, playwright
        checks.append(("Dependencies", True, "All required packages installed"))
    except ImportError as e:
        checks.append(("Dependencies", False, f"Missing: {e.name}"))
    
    # Check 5: Project structure
    required_dirs = ["config", "setup", "automation", "notion_integration", "core", "utils"]
    dirs_exist = sum(Path(d).exists() for d in required_dirs)
    checks.append(("Project Structure", dirs_exist == len(required_dirs), f"{dirs_exist}/{len(required_dirs)} directories"))
    
    # Check 6: Data directories
    data_dir = Path("data")
    logs_dir = Path("logs")
    data_ready = data_dir.exists() and logs_dir.exists()
    checks.append(("Data Directories", data_ready, "Created" if data_ready else "Missing"))
    
    # Display results
    passed = 0
    total = len(checks)
    
    for check_name, success, details in checks:
        status = "✅" if success else "❌"
        print(f"{status} {check_name}: {details}")
        if success:
            passed += 1
    
    print(f"\nOverall Status: {passed}/{total} checks passed")
    
    # Final assessment
    if passed == total:
        print("\n🎉 System is READY!")
        print("\nNext steps:")
        print("1. Get Notion API key from https://www.notion.so/my-integrations")
        print("2. Update NOTION_API_KEY in .env file")
        print("3. Run: python3 main.py setup")
        print("4. Test: python3 main.py apply --dry-run")
        return True
    elif passed >= total - 2:
        print("\n⚠️  System is MOSTLY ready!")
        print("Fix the failing checks above and you'll be good to go.")
        return True
    else:
        print("\n❌ System needs more setup.")
        print("Please address the failing checks above.")
        return False

def show_quick_commands():
    """Show quick reference commands."""
    print("\n📋 Quick Reference Commands")
    print("-" * 30)
    print("Setup:     python3 main.py setup")
    print("Apply:     python3 main.py apply --dry-run")
    print("Schedule:  python3 main.py schedule --time 08:00")
    print("Status:    python3 main.py status")
    print("Manage:    python3 scripts/manage.py status")
    print("Test:      python3 test_installation.py")
    print("Demo:      python3 demo.py")

def show_api_key_instructions():
    """Show instructions for getting API keys."""
    print("\n🔑 API Key Setup Instructions")
    print("-" * 35)
    print("Notion API Key (Required):")
    print("1. Go to https://www.notion.so/my-integrations")
    print("2. Click 'New integration'")
    print("3. Name it 'Job Application Agent'")
    print("4. Copy the API key")
    print("5. Replace 'your_notion_api_key_here' in .env file")
    print()
    print("OpenAI API Key: ✅ Already configured!")

if __name__ == "__main__":
    try:
        system_ready = check_system_ready()
        show_quick_commands()
        show_api_key_instructions()
        
        if system_ready:
            print("\n🚀 You're ready to start automating job applications!")
        
    except Exception as e:
        print(f"\n💥 Check failed with error: {e}")
        sys.exit(1)
