JOHN SMITH
Software Engineering Student

Email: <EMAIL>
Phone: (*************
LinkedIn: https://www.linkedin.com/in/johnsmith
GitHub: https://github.com/johnsmith

EDUCATION
Bachelor of Science in Computer Science
University of Technology, Expected May 2024
GPA: 3.8/4.0

TECHNICAL SKILLS
Programming Languages: Python, Java, JavaScript, C++
Web Technologies: React, Node.js, HTML, CSS
Databases: MySQL, PostgreSQL, MongoDB
Tools: Git, Docker, AWS, Linux

PROJECTS
Job Application Automation System
- Built intelligent job application automation using Python and browser automation
- Integrated AI for smart form filling and response generation
- Implemented secure data encryption and Notion API integration

E-commerce Web Application
- Developed full-stack web application using React and Node.js
- Implemented user authentication and payment processing
- Deployed on AWS with CI/CD pipeline

EXPERIENCE
Software Development Intern
Tech Company, Summer 2023
- Developed REST APIs using Python and Flask
- Collaborated with team of 5 developers on agile projects
- Improved application performance by 30%

CERTIFICATIONS
AWS Certified Cloud Practitioner
Google Cloud Associate Cloud Engineer
